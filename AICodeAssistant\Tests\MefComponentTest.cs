using System;
using System.ComponentModel.Composition;
using System.Linq;
using Microsoft.VisualStudio.ComponentModelHost;
using Microsoft.VisualStudio.Language.Intellisense.AsyncCompletion;
using Microsoft.VisualStudio.Shell;
using AICodeAssistant.Features.Completion;

namespace AICodeAssistant.Tests
{
    /// <summary>
    /// MEF 组件测试类，用于验证补全源提供者是否正确注册
    /// </summary>
    public static class MefComponentTest
    {
        /// <summary>
        /// 测试 MEF 组件注册
        /// </summary>
        public static void TestMefRegistration()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();
                
                ActivityLog.LogInformation("MefComponentTest", "开始测试 MEF 组件注册");

                // 获取组件模型服务
                var componentModel = Package.GetGlobalService(typeof(SComponentModel)) as IComponentModel;
                if (componentModel == null)
                {
                    ActivityLog.LogError("MefComponentTest", "无法获取组件模型服务");
                    return;
                }

                ActivityLog.LogInformation("MefComponentTest", "成功获取组件模型服务");

                // 获取导出提供者
                var exportProvider = componentModel.DefaultExportProvider;
                if (exportProvider == null)
                {
                    ActivityLog.LogError("MefComponentTest", "无法获取导出提供者");
                    return;
                }

                ActivityLog.LogInformation("MefComponentTest", "成功获取导出提供者");

                // 获取所有补全源提供者
                var completionProviders = exportProvider.GetExports<IAsyncCompletionSourceProvider>();
                ActivityLog.LogInformation("MefComponentTest", $"找到 {completionProviders.Count()} 个补全源提供者");

                // 列出所有提供者
                foreach (var provider in completionProviders)
                {
                    var providerType = provider.Value.GetType();
                    var providerName = providerType.Name;
                    var providerNamespace = providerType.Namespace;
                    
                    ActivityLog.LogInformation("MefComponentTest", $"发现提供者: {providerNamespace}.{providerName}");
                    
                    // 检查是否是我们的提供者
                    if (providerName == "AICompletionSourceProvider")
                    {
                        ActivityLog.LogInformation("MefComponentTest", "✅ 找到 AI 补全源提供者！");
                        
                        // 获取提供者的元数据
                        var metadata = provider.Metadata;
                        if (metadata != null)
                        {
                            ActivityLog.LogInformation("MefComponentTest", "提供者元数据:");
                            foreach (var kvp in metadata)
                            {
                                ActivityLog.LogInformation("MefComponentTest", $"  {kvp.Key}: {kvp.Value}");
                            }
                        }
                        
                        return;
                    }
                }

                ActivityLog.LogWarning("MefComponentTest", "❌ 未找到 AI 补全源提供者");
                
                // 尝试直接创建实例进行测试
                TestDirectInstantiation();
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("MefComponentTest", $"MEF 组件测试失败: {ex.Message}");
                ActivityLog.LogError("MefComponentTest", $"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试直接实例化
        /// </summary>
        private static void TestDirectInstantiation()
        {
            try
            {
                ActivityLog.LogInformation("MefComponentTest", "尝试直接实例化 AICompletionSourceProvider");
                
                var provider = new AICompletionSourceProvider();
                if (provider != null)
                {
                    ActivityLog.LogInformation("MefComponentTest", "✅ 成功创建 AICompletionSourceProvider 实例");
                    
                    // 测试 GetOrCreate 方法（需要模拟 ITextView）
                    ActivityLog.LogInformation("MefComponentTest", "AICompletionSourceProvider 实例化成功，但需要 ITextView 来测试 GetOrCreate 方法");
                }
                else
                {
                    ActivityLog.LogError("MefComponentTest", "❌ 无法创建 AICompletionSourceProvider 实例");
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("MefComponentTest", $"直接实例化测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试补全源创建
        /// </summary>
        public static void TestCompletionSourceCreation()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();
                
                ActivityLog.LogInformation("MefComponentTest", "开始测试补全源创建");

                // 这里需要一个真实的 ITextView 实例来测试
                // 在实际的 Visual Studio 环境中，这将由编辑器提供
                ActivityLog.LogInformation("MefComponentTest", "补全源创建测试需要在实际的编辑器环境中进行");
                
                // 记录一些有用的信息
                ActivityLog.LogInformation("MefComponentTest", "要测试补全功能，请:");
                ActivityLog.LogInformation("MefComponentTest", "1. 打开一个 C# 文件");
                ActivityLog.LogInformation("MefComponentTest", "2. 输入一些代码");
                ActivityLog.LogInformation("MefComponentTest", "3. 输入触发字符（如 '.' 或按 Ctrl+Space）");
                ActivityLog.LogInformation("MefComponentTest", "4. 查看活动日志中的 AICompletionSource 消息");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("MefComponentTest", $"补全源创建测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            ActivityLog.LogInformation("MefComponentTest", "=== 开始 MEF 组件测试 ===");
            
            TestMefRegistration();
            TestCompletionSourceCreation();
            
            ActivityLog.LogInformation("MefComponentTest", "=== MEF 组件测试完成 ===");
        }
    }
}
