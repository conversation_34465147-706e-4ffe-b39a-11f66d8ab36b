using System;
using System.Threading;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using Microsoft.VisualStudio.Text;
using Microsoft.VisualStudio.Text.Editor;
using Microsoft.VisualStudio.Shell;
using AICodeAssistant.Services;
using AICodeAssistant.UI;
using AICodeAssistant.Resources;

namespace AICodeAssistant.Features.Completion
{
    /// <summary>
    /// Ghost Text会话管理器
    /// 负责管理单个文本视图的Ghost Text生成和缓存
    /// </summary>
    public class GhostTextSession : IDisposable
    {
        private readonly ITextView _textView;
        private readonly ILlmProvider _llmProvider;
        private readonly CompletionSettings _settings;
        private readonly object _lockObject = new object();

        private CancellationTokenSource _currentRequest;
        private string _lastSuggestion;
        private SnapshotPoint? _lastSuggestionPosition;
        private DateTime _lastRequestTime = DateTime.MinValue;

        // 防抖动和缓存设置
        private const int DebounceDelayMs = 600;  // 防抖动延迟
        private const int CacheValidityMs = 5000; // 缓存有效期
        private const int MaxSuggestionLength = 100; // 最大建议长度

        public GhostTextSession(ITextView textView, ILlmProvider llmProvider, CompletionSettings settings)
        {
            _textView = textView ?? throw new ArgumentNullException(nameof(textView));
            _llmProvider = llmProvider ?? throw new ArgumentNullException(nameof(llmProvider));
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
        }

        /// <summary>
        /// 获取Ghost Text建议
        /// </summary>
        public async Task<IGhostTextSuggestion> GetSuggestionAsync(SnapshotPoint position, CancellationToken cancellationToken)
        {
            try
            {
                lock (_lockObject)
                {
                    // 取消之前的请求
                    _currentRequest?.Cancel();
                    _currentRequest = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                }

                var requestToken = _currentRequest.Token;

                // 检查缓存
                var cachedSuggestion = GetCachedSuggestion(position);
                if (cachedSuggestion != null)
                {
                    ActivityLog.LogInformation("GhostTextSession", "使用缓存的Ghost Text建议");
                    return cachedSuggestion;
                }

                // 防抖动处理
                var timeSinceLastRequest = DateTime.Now - _lastRequestTime;
                if (timeSinceLastRequest.TotalMilliseconds < DebounceDelayMs)
                {
                    var delayTime = DebounceDelayMs - (int)timeSinceLastRequest.TotalMilliseconds;
                    await Task.Delay(delayTime, requestToken);
                }

                _lastRequestTime = DateTime.Now;

                // 检查是否已取消
                if (requestToken.IsCancellationRequested)
                {
                    return null;
                }

                // 提取代码上下文
                var context = await ExtractContextAsync(position, requestToken);
                if (context == null || requestToken.IsCancellationRequested)
                {
                    return null;
                }

                // 构建Ghost Text专用提示词
                var prompt = BuildGhostTextPrompt(context);

                ActivityLog.LogInformation("GhostTextSession", 
                    $"发送Ghost Text请求，上下文长度: {context.BeforeCaret?.Length ?? 0}");

                // 调用AI生成建议
                var response = await _llmProvider.SendAsync(prompt, false, null, requestToken);

                if (string.IsNullOrWhiteSpace(response) || requestToken.IsCancellationRequested)
                {
                    return null;
                }

                // 清理和处理响应
                var cleanedSuggestion = CleanGhostTextResponse(response);
                if (string.IsNullOrWhiteSpace(cleanedSuggestion))
                {
                    return null;
                }

                ActivityLog.LogInformation("GhostTextSession", 
                    $"生成Ghost Text建议: '{cleanedSuggestion}'");

                // 缓存结果
                _lastSuggestion = cleanedSuggestion;
                _lastSuggestionPosition = position;

                // 创建Ghost Text建议
                var applicableSpan = new SnapshotSpan(position, 0);
                return new GhostTextSuggestion(cleanedSuggestion, applicableSpan);
            }
            catch (OperationCanceledException)
            {
                ActivityLog.LogInformation("GhostTextSession", "Ghost Text请求被取消");
                return null;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextSession", $"生成Ghost Text失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取缓存的建议
        /// </summary>
        private IGhostTextSuggestion GetCachedSuggestion(SnapshotPoint position)
        {
            try
            {
                if (string.IsNullOrEmpty(_lastSuggestion) || !_lastSuggestionPosition.HasValue)
                {
                    return null;
                }

                // 检查位置是否匹配
                if (_lastSuggestionPosition.Value.Position != position.Position)
                {
                    return null;
                }

                // 检查缓存是否过期
                var timeSinceLastRequest = DateTime.Now - _lastRequestTime;
                if (timeSinceLastRequest.TotalMilliseconds > CacheValidityMs)
                {
                    return null;
                }

                var applicableSpan = new SnapshotSpan(position, 0);
                return new GhostTextSuggestion(_lastSuggestion, applicableSpan);
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextSession", $"获取缓存建议失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 提取代码上下文
        /// </summary>
        private async Task<CodeContext> ExtractContextAsync(SnapshotPoint position, CancellationToken cancellationToken)
        {
            try
            {
                // 使用现有的上下文提取器，但限制行数以提高性能
                var limitedLines = Math.Min(_settings.ContextLines, 15); // 限制最大15行
                var context = await ContextExtractor.ExtractContextAsync(_textView, limitedLines);

                if (context != null)
                {
                    ActivityLog.LogInformation("GhostTextSession", 
                        $"上下文提取成功 - 文件: {context.FilePath}, 光标前: {context.BeforeCaret?.Length ?? 0} 字符");
                }

                return context;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextSession", $"上下文提取失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 构建Ghost Text专用提示词
        /// </summary>
        private string BuildGhostTextPrompt(CodeContext context)
        {
            try
            {
                var language = GetLanguageFromFilePath(context.FilePath);

                // Ghost Text专用的简洁提示词
                var systemPrompt = $@"You are a {language} code completion assistant. Complete the code at cursor position.

RULES:
1. Return ONLY the code to insert, no explanations
2. Keep it very short (1-3 words max)
3. Complete the current statement/expression
4. Ensure syntactic correctness
5. Don't repeat existing code

Language: {language}";

                var userPrompt = $@"Complete this {language} code:
{context.BeforeCaret}▮{context.AfterCaret}

Insert at ▮:";

                return $"{systemPrompt}\n\n{userPrompt}";
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextSession", $"构建提示词失败: {ex.Message}");
                return $"Complete this code:\n{context.BeforeCaret ?? string.Empty}";
            }
        }

        /// <summary>
        /// 清理Ghost Text响应
        /// </summary>
        private string CleanGhostTextResponse(string response)
        {
            if (string.IsNullOrWhiteSpace(response))
                return string.Empty;

            try
            {
                // 移除代码块标记
                response = Regex.Replace(response, @"```[\w]*\n?", "", RegexOptions.IgnoreCase);
                response = Regex.Replace(response, @"```", "", RegexOptions.IgnoreCase);

                // 移除常见的前缀
                var prefixesToRemove = new[]
                {
                    "Here's the completion:",
                    "The completion is:",
                    "Complete:",
                    "Insert:",
                    "Add:",
                    "Code:",
                    "Result:"
                };

                foreach (var prefix in prefixesToRemove)
                {
                    if (response.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
                    {
                        response = response.Substring(prefix.Length).TrimStart();
                        break;
                    }
                }

                // 只取第一行的第一个有意义的部分
                var lines = response.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                if (lines.Length > 0)
                {
                    response = lines[0].Trim();
                }

                // 移除引号
                if (response.StartsWith("\"") && response.EndsWith("\""))
                {
                    response = response.Substring(1, response.Length - 2);
                }

                // 限制长度 - Ghost Text应该很短
                if (response.Length > MaxSuggestionLength)
                {
                    // 尝试在合适的位置截断
                    var truncateAt = response.LastIndexOf(' ', MaxSuggestionLength);
                    if (truncateAt > MaxSuggestionLength / 2)
                    {
                        response = response.Substring(0, truncateAt);
                    }
                    else
                    {
                        response = response.Substring(0, MaxSuggestionLength);
                    }
                }

                // 移除末尾的分号（如果不是完整语句）
                response = response.TrimEnd(';', ',', '.');

                return response.Trim();
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextSession", $"清理响应失败: {ex.Message}");
                return response?.Trim() ?? string.Empty;
            }
        }

        /// <summary>
        /// 从文件路径获取编程语言
        /// </summary>
        private string GetLanguageFromFilePath(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return "code";

            var extension = System.IO.Path.GetExtension(filePath)?.ToLowerInvariant();
            return extension switch
            {
                ".cs" => "C#",
                ".vb" => "VB.NET",
                ".cpp" or ".c" or ".h" or ".hpp" => "C++",
                ".js" => "JavaScript",
                ".ts" => "TypeScript",
                ".py" => "Python",
                ".java" => "Java",
                ".xml" or ".xaml" => "XML",
                ".json" => "JSON",
                ".html" => "HTML",
                ".css" => "CSS",
                ".sql" => "SQL",
                _ => "code"
            };
        }

        /// <summary>
        /// 清理缓存
        /// </summary>
        public void ClearCache()
        {
            lock (_lockObject)
            {
                _lastSuggestion = null;
                _lastSuggestionPosition = null;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                lock (_lockObject)
                {
                    _currentRequest?.Cancel();
                    _currentRequest?.Dispose();
                    _currentRequest = null;
                }

                ClearCache();
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextSession", $"释放资源失败: {ex.Message}");
            }
        }
    }
}
