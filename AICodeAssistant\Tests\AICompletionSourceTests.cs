using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.VisualStudio.Language.Intellisense.AsyncCompletion.Data;
using Microsoft.VisualStudio.Text;
using Microsoft.VisualStudio.Text.Editor;
using AICodeAssistant.Features.Completion;
using AICodeAssistant.Services;
using AICodeAssistant.UI;

namespace AICodeAssistant.Tests
{
    /// <summary>
    /// AI 代码补全源测试类
    /// </summary>
    public class AICompletionSourceTests
    {
        /// <summary>
        /// 测试补全触发条件
        /// </summary>
        public void TestShouldTriggerCompletion()
        {
            try
            {
                // 创建模拟的设置
                var settings = new CompletionSettings
                {
                    Enabled = true,
                    ContextLines = 10,
                    MaxTokens = 1024,
                    Temperature = 0.7
                };

                // 创建模拟的 LLM 提供者
                var mockProvider = new MockLlmProvider();

                // 注意：在实际测试中，需要创建模拟的 ITextView
                // 这里只是展示测试结构
                Console.WriteLine("代码补全测试：触发条件检查");
                Console.WriteLine($"设置已启用: {settings.Enabled}");
                Console.WriteLine($"上下文行数: {settings.ContextLines}");
                Console.WriteLine($"最大 Token 数: {settings.MaxTokens}");
                Console.WriteLine($"温度参数: {settings.Temperature}");
                Console.WriteLine($"LLM 提供者可用: {mockProvider.IsAvailable}");
                
                Console.WriteLine("✅ 代码补全基础配置测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 代码补全测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试提示词构建
        /// </summary>
        public void TestPromptBuilding()
        {
            try
            {
                // 创建模拟的代码上下文
                var context = new CodeContext
                {
                    FilePath = "TestFile.cs",
                    Language = "csharp",
                    BeforeCaret = "public class TestClass\n{\n    public void TestMethod()\n    {\n        var result = ",
                    AfterCaret = ";\n    }\n}",
                    CaretLine = 5,
                    CaretPosition = 21
                };

                Console.WriteLine("代码补全测试：提示词构建");
                Console.WriteLine($"文件路径: {context.FilePath}");
                Console.WriteLine($"编程语言: {context.Language}");
                Console.WriteLine($"光标前代码: {context.BeforeCaret?.Substring(Math.Max(0, context.BeforeCaret.Length - 50))}");
                Console.WriteLine($"光标后代码: {context.AfterCaret?.Substring(0, Math.Min(50, context.AfterCaret?.Length ?? 0))}");
                Console.WriteLine($"行号: {context.CaretLine}, 位置: {context.CaretPosition}");
                
                Console.WriteLine("✅ 提示词构建测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 提示词构建测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试 AI 响应解析
        /// </summary>
        public void TestResponseParsing()
        {
            try
            {
                // 模拟 AI 响应
                var mockResponse = @"string.Empty
Console.WriteLine(""Hello World"")
DateTime.Now
result.ToString()";

                Console.WriteLine("代码补全测试：AI 响应解析");
                Console.WriteLine($"模拟 AI 响应:\n{mockResponse}");
                
                // 解析响应
                var suggestions = mockResponse.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                Console.WriteLine($"解析出 {suggestions.Length} 个建议:");
                
                for (int i = 0; i < suggestions.Length; i++)
                {
                    var suggestion = suggestions[i].Trim();
                    Console.WriteLine($"  {i + 1}. {suggestion}");
                }
                
                Console.WriteLine("✅ AI 响应解析测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AI 响应解析测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== AI 代码补全功能测试 ===\n");
            
            var tests = new AICompletionSourceTests();
            
            tests.TestShouldTriggerCompletion();
            Console.WriteLine();
            
            tests.TestPromptBuilding();
            Console.WriteLine();
            
            tests.TestResponseParsing();
            Console.WriteLine();
            
            Console.WriteLine("=== 测试完成 ===");
        }
    }

    /// <summary>
    /// 模拟的 LLM 提供者，用于测试
    /// </summary>
    public class MockLlmProvider : ILlmProvider
    {
        public string Name => "Mock Provider";
        public bool IsAvailable => true;

        public Task<string> SendAsync(string prompt, bool stream = false, IProgress<string> progress = null, CancellationToken cancellationToken = default)
        {
            // 模拟 AI 响应
            var mockResponse = @"string.Empty
Console.WriteLine(""Hello World"")
DateTime.Now
result.ToString()";
            
            return Task.FromResult(mockResponse);
        }
    }
}
