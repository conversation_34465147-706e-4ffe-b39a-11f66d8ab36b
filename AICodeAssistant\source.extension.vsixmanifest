<?xml version="1.0" encoding="utf-8"?>
<PackageManifest Version="2.0.0" xmlns="http://schemas.microsoft.com/developer/vsx-schema/2011" xmlns:d="http://schemas.microsoft.com/developer/vsx-schema-design/2011">
  <Metadata>
    <Identity Id="AICodeAssistant.17d50e71-ddb8-4a0c-8bb8-0dd246bd3494" Version="1.0" Language="en-US" Publisher="AI Code Assistant Team" />
    <DisplayName>Ollama AI Code Assistant</DisplayName>
    <Description>一个强大的 Visual Studio 扩展，集成了 AI 代码补全和智能聊天功能，支持 OpenAI 和 Ollama 两种 LLM 提供者。提供实时代码补全建议、上下文感知聊天助手、快捷命令等功能。</Description>
    <Icon>Resources\Icon.png</Icon>
    <PreviewImage>Resources\Icon.png</PreviewImage>
    <Tags>AI, Code Completion, Chat Assistant, Ollama, OpenAI, IntelliSense, Productivity</Tags>
  </Metadata>
  <Installation>
    <InstallationTarget Id="Microsoft.VisualStudio.Community" Version="[17.0, 18.0)">
      <ProductArchitecture>amd64</ProductArchitecture>
    </InstallationTarget>
  </Installation>
  <Prerequisites>
    <Prerequisite Id="Microsoft.VisualStudio.Component.CoreEditor" Version="[17.0,)" DisplayName="Visual Studio core editor" />
  </Prerequisites>
  <Assets>
    <Asset Type="Microsoft.VisualStudio.VsPackage" d:Source="Project" d:ProjectName="%CurrentProject%" Path="|%CurrentProject%;PkgdefProjectOutputGroup|" />
    <Asset Type="Microsoft.VisualStudio.MefComponent" d:Source="Project" d:ProjectName="%CurrentProject%" Path="|%CurrentProject%|" />
  </Assets>
</PackageManifest>
