using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.ComponentModel.Composition;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.VisualStudio.Language.Intellisense.AsyncCompletion;
using Microsoft.VisualStudio.Language.Intellisense.AsyncCompletion.Data;
using Microsoft.VisualStudio.Text;
using Microsoft.VisualStudio.Text.Editor;
using Microsoft.VisualStudio.Utilities;
using Microsoft.VisualStudio.Shell;
using AICodeAssistant.Services;
using AICodeAssistant.UI;
using AICodeAssistant.Resources;

namespace AICodeAssistant.Features.Completion
{
    /// <summary>
    /// 标准的AI内联补全提供者，使用Visual Studio标准API
    /// 基于业界最佳实践重新实现
    /// </summary>
    [Export(typeof(IAsyncCompletionSourceProvider))]
    [Name("AI Inline Completion Standard")]
    [ContentType("code")]
    [ContentType("text")]
    [Order(Before = "default")]
    public class StandardInlineCompletionProvider : IAsyncCompletionSourceProvider
    {
        public IAsyncCompletionSource GetOrCreate(ITextView textView)
        {
            try
            {
                // 检查是否已经创建
                if (textView.Properties.TryGetProperty(typeof(StandardInlineCompletionSource), out StandardInlineCompletionSource existingSource))
                {
                    return existingSource;
                }

                ActivityLog.LogInformation("StandardInlineCompletionProvider", "创建标准内联补全源");

                // 加载设置
                var settings = LoadSettings();
                if (settings?.Completion?.Enabled != true)
                {
                    ActivityLog.LogInformation("StandardInlineCompletionProvider", "内联补全功能未启用");
                    return null;
                }

                // 获取LLM提供者
                var llmProvider = GetLlmProvider(settings);
                if (llmProvider == null || !llmProvider.IsAvailable)
                {
                    ActivityLog.LogWarning("StandardInlineCompletionProvider", "LLM提供者不可用");
                    return null;
                }

                // 创建新的补全源
                var completionSource = new StandardInlineCompletionSource(textView, llmProvider, settings.Completion);
                textView.Properties.AddProperty(typeof(StandardInlineCompletionSource), completionSource);

                ActivityLog.LogInformation("StandardInlineCompletionProvider", "标准内联补全源创建成功");
                return completionSource;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("StandardInlineCompletionProvider", $"创建补全源失败: {ex.Message}");
                return null;
            }
        }

        private AppSettings LoadSettings()
        {
            try
            {
                return SettingsManager.LoadSettings() ?? AppSettings.CreateDefault();
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("StandardInlineCompletionProvider", $"加载设置失败: {ex.Message}");
                return AppSettings.CreateDefault();
            }
        }

        private ILlmProvider GetLlmProvider(AppSettings settings)
        {
            try
            {
                var options = CreateLlmOptions(settings);
                return LlmProviderFactory.CreateProvider(settings.ProviderType, options);
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("StandardInlineCompletionProvider", $"创建LLM提供者失败: {ex.Message}");
                return null;
            }
        }

        private LlmOptions CreateLlmOptions(AppSettings settings)
        {
            return settings.ProviderType switch
            {
                LlmProviderType.OpenAI => new LlmOptions
                {
                    ApiKey = settings.OpenAI.ApiKey,
                    ApiBase = settings.OpenAI.ApiBase,
                    Model = settings.OpenAI.Model,
                    Temperature = 0.2, // 更低的温度获得更确定的结果
                    MaxTokens = 256,   // 限制内联补全长度
                    TimeoutSeconds = 10
                },
                LlmProviderType.Ollama => new LlmOptions
                {
                    ApiBase = settings.Ollama.ApiBase,
                    Model = settings.Ollama.Model,
                    Temperature = 0.2,
                    MaxTokens = 256,
                    TimeoutSeconds = 15
                },
                _ => LlmProviderFactory.GetDefaultOptions(LlmProviderType.Ollama)
            };
        }
    }

    /// <summary>
    /// 标准内联补全源，实现更好的用户体验
    /// </summary>
    public class StandardInlineCompletionSource : IAsyncCompletionSource
    {
        private readonly ITextView _textView;
        private readonly ILlmProvider _llmProvider;
        private readonly CompletionSettings _settings;
        private readonly object _lockObject = new object();
        
        private CancellationTokenSource _currentRequest;
        private DateTime _lastTriggerTime = DateTime.MinValue;
        private string _lastSuggestion;
        private SnapshotPoint? _lastSuggestionPosition;

        // 配置参数
        private const int TriggerDelayMs = 800;  // 触发延迟
        private const int MinLineLength = 5;     // 最小行长度
        private const int MaxSuggestionLength = 150; // 最大建议长度

        public StandardInlineCompletionSource(ITextView textView, ILlmProvider llmProvider, CompletionSettings settings)
        {
            _textView = textView ?? throw new ArgumentNullException(nameof(textView));
            _llmProvider = llmProvider ?? throw new ArgumentNullException(nameof(llmProvider));
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
        }

        public CompletionStartData InitializeCompletion(CompletionTrigger trigger, SnapshotPoint triggerLocation, CancellationToken token)
        {
            try
            {
                // 检查是否应该触发内联补全
                if (!ShouldTriggerInlineCompletion(trigger, triggerLocation))
                {
                    return CompletionStartData.DoesNotParticipateInCompletion;
                }

                ActivityLog.LogInformation("StandardInlineCompletionSource", 
                    $"触发内联补全 - 位置: {triggerLocation.Position}");

                // 返回参与补全，但不显示传统的下拉列表
                return new CompletionStartData(
                    participation: CompletionParticipation.ProvidesItems,
                    applicableToSpan: new SnapshotSpan(triggerLocation, 0));
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("StandardInlineCompletionSource", $"初始化补全失败: {ex.Message}");
                return CompletionStartData.DoesNotParticipateInCompletion;
            }
        }

        public async Task<CompletionContext> GetCompletionContextAsync(
            IAsyncCompletionSession session, 
            CompletionTrigger trigger, 
            SnapshotPoint triggerLocation, 
            SnapshotSpan applicableToSpan, 
            CancellationToken token)
        {
            try
            {
                // 对于内联补全，我们返回一个特殊的补全项
                // 这个补全项会触发内联建议的显示
                var inlineCompletionItem = await CreateInlineCompletionItemAsync(triggerLocation, token);
                
                if (inlineCompletionItem != null)
                {
                    return new CompletionContext(new[] { inlineCompletionItem }.ToImmutableArray());
                }

                return new CompletionContext(ImmutableArray<CompletionItem>.Empty);
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("StandardInlineCompletionSource", $"获取补全上下文失败: {ex.Message}");
                return new CompletionContext(ImmutableArray<CompletionItem>.Empty);
            }
        }

        public Task<object> GetDescriptionAsync(IAsyncCompletionSession session, CompletionItem item, CancellationToken token)
        {
            return Task.FromResult<object>("AI 内联建议");
        }

        /// <summary>
        /// 判断是否应该触发内联补全
        /// </summary>
        private bool ShouldTriggerInlineCompletion(CompletionTrigger trigger, SnapshotPoint triggerLocation)
        {
            try
            {
                // 检查触发时间间隔
                var now = DateTime.Now;
                if ((now - _lastTriggerTime).TotalMilliseconds < TriggerDelayMs)
                {
                    return false;
                }

                // 获取当前行
                var line = triggerLocation.GetContainingLine();
                var lineText = line.GetText();
                var position = triggerLocation.Position - line.Start.Position;

                // 检查是否在行尾或接近行尾
                if (position < lineText.TrimEnd().Length - 2)
                {
                    return false;
                }

                // 检查行长度
                if (lineText.Trim().Length < MinLineLength)
                {
                    return false;
                }

                // 检查文件类型
                var filePath = GetFilePath();
                if (!IsSupportedFileType(filePath))
                {
                    return false;
                }

                // 检查是否在注释或字符串中
                if (IsInCommentOrString(triggerLocation))
                {
                    return false;
                }

                _lastTriggerTime = now;
                return true;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("StandardInlineCompletionSource", $"判断触发条件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建内联补全项
        /// </summary>
        private async Task<CompletionItem> CreateInlineCompletionItemAsync(SnapshotPoint triggerLocation, CancellationToken token)
        {
            try
            {
                // 取消之前的请求
                lock (_lockObject)
                {
                    _currentRequest?.Cancel();
                    _currentRequest = CancellationTokenSource.CreateLinkedTokenSource(token);
                }

                var requestToken = _currentRequest.Token;

                // 提取上下文
                var context = await ContextExtractor.ExtractContextAsync(_textView, _settings.ContextLines);
                if (context == null || requestToken.IsCancellationRequested)
                {
                    return null;
                }

                // 构建提示词
                var prompt = BuildInlinePrompt(context);
                
                ActivityLog.LogInformation("StandardInlineCompletionSource", 
                    $"发送内联补全请求，上下文长度: {context.BeforeCaret?.Length ?? 0}");

                // 调用AI
                var response = await _llmProvider.SendAsync(prompt, false, null, requestToken);
                if (string.IsNullOrWhiteSpace(response) || requestToken.IsCancellationRequested)
                {
                    return null;
                }

                // 清理响应
                var cleanedSuggestion = CleanInlineResponse(response);
                if (string.IsNullOrWhiteSpace(cleanedSuggestion))
                {
                    return null;
                }

                ActivityLog.LogInformation("StandardInlineCompletionSource", 
                    $"生成内联建议: '{cleanedSuggestion}'");

                // 缓存建议
                _lastSuggestion = cleanedSuggestion;
                _lastSuggestionPosition = triggerLocation;

                // 创建一个特殊的补全项，用于显示内联建议
                var completionItem = new CompletionItem(
                    displayText: "⚡ AI建议", // 显示一个提示
                    source: this,
                    applicableToSpan: new SnapshotSpan(triggerLocation, 0));

                // 添加内联建议数据
                completionItem.Properties.AddProperty("InlineSuggestion", cleanedSuggestion);
                completionItem.Properties.AddProperty("IsInlineCompletion", true);

                return completionItem;
            }
            catch (OperationCanceledException)
            {
                return null;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("StandardInlineCompletionSource", $"创建内联补全项失败: {ex.Message}");
                return null;
            }
        }

        // 其他辅助方法...
        private string GetFilePath()
        {
            try
            {
                if (_textView.TextBuffer.Properties.TryGetProperty(typeof(ITextDocument), out ITextDocument document))
                {
                    return document.FilePath ?? "Unknown";
                }
            }
            catch { }
            return "Unknown";
        }

        private bool IsSupportedFileType(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || filePath == "Unknown")
                return true;

            var extension = System.IO.Path.GetExtension(filePath)?.ToLowerInvariant();
            return Constants.FileExtensions.SupportedCodeFiles.Contains(extension);
        }

        private bool IsInCommentOrString(SnapshotPoint position)
        {
            // 简单的检查，可以根据需要扩展
            var line = position.GetContainingLine();
            var lineText = line.GetText();
            var pos = position.Position - line.Start.Position;
            
            if (pos > 0 && pos < lineText.Length)
            {
                // 检查是否在字符串中
                var beforeCursor = lineText.Substring(0, pos);
                var quoteCount = beforeCursor.Count(c => c == '"');
                if (quoteCount % 2 == 1) return true;

                // 检查是否在注释中
                if (beforeCursor.TrimStart().StartsWith("//")) return true;
            }

            return false;
        }

        private string BuildInlinePrompt(CodeContext context)
        {
            var language = GetLanguageFromFilePath(context.FilePath);
            
            var systemPrompt = $@"Complete the {language} code at cursor position. Rules:
1. Return ONLY the code to insert, no explanations
2. Keep it short and relevant (max 1-2 lines)
3. Ensure syntactic correctness
4. Don't repeat existing code";

            var userPrompt = $@"Code:
{context.BeforeCaret}▮{context.AfterCaret}

Complete at ▮:";

            return $"{systemPrompt}\n\n{userPrompt}";
        }

        private string CleanInlineResponse(string response)
        {
            if (string.IsNullOrWhiteSpace(response))
                return string.Empty;

            // 移除代码块标记
            response = System.Text.RegularExpressions.Regex.Replace(response, @"```[\w]*\n?", "", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            response = System.Text.RegularExpressions.Regex.Replace(response, @"```", "", System.Text.RegularExpressions.RegexOptions.IgnoreCase);

            // 只取第一行
            var lines = response.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
            if (lines.Length > 0)
            {
                response = lines[0].Trim();
            }

            // 限制长度
            if (response.Length > MaxSuggestionLength)
            {
                response = response.Substring(0, MaxSuggestionLength);
            }

            return response.Trim();
        }

        private string GetLanguageFromFilePath(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return "text";

            var extension = System.IO.Path.GetExtension(filePath)?.ToLowerInvariant();
            return extension switch
            {
                ".cs" => "C#",
                ".js" => "JavaScript",
                ".ts" => "TypeScript",
                ".py" => "Python",
                ".java" => "Java",
                _ => "code"
            };
        }
    }
}
