using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.VisualStudio.Shell;
using Microsoft.VisualStudio.Threading;

namespace AICodeAssistant.Services
{
    /// <summary>
    /// Ollama API 提供者实现
    /// </summary>
    public class OllamaProvider : ILlmProvider, IDisposable
    {
        private readonly LlmOptions _options;
        private readonly HttpClient _httpClient;
        private readonly OllamaModelCache _modelCache;

        public string Name => "Ollama";
        public bool IsAvailable
        {
            get
            {
                try
                {
                    return ThreadHelper.JoinableTaskFactory.Run(async () => await IsOllamaRunningAsync().ConfigureAwait(false));
                }
                catch
                {
                    return false;
                }
            }
        }

        public OllamaProvider(LlmOptions options)
        {
            _options = options ?? throw new ArgumentNullException(nameof(options));
            _httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromSeconds(_options.TimeoutSeconds)
            };
            _modelCache = new OllamaModelCache();
        }

        public async Task<string> SendAsync(string prompt, bool stream = false, IProgress<string> progress = null, CancellationToken cancellationToken = default)
        {
            // 检查是否是 messages 格式的 JSON
            object requestBody;
            string apiUrl;
            bool isChatFormat = IsMessagesFormat(prompt);

            if (isChatFormat)
            {
                // 使用 chat completions API
                apiUrl = GetApiUrl(Constants.Ollama.ChatEndpoint);
                requestBody = CreateChatRequestBody(prompt, stream);
            }
            else
            {
                // 使用传统的 generate API
                apiUrl = GetApiUrl(Constants.Ollama.GenerateEndpoint);
                requestBody = new
                {
                    model = string.IsNullOrEmpty(_options.Model) ? Constants.Defaults.DefaultOllamaModel : _options.Model,
                    prompt = prompt,
                    stream = stream,
                    options = new
                    {
                        temperature = _options.Temperature,
                        num_predict = _options.MaxTokens
                    }
                };
            }

            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // 详细的请求日志
            ActivityLog.LogInformation("OllamaProvider", $"=== Ollama API 请求开始 ===");
            ActivityLog.LogInformation("OllamaProvider", $"请求 URL: {apiUrl}");
            ActivityLog.LogInformation("OllamaProvider", $"API 格式: {(isChatFormat ? "Chat" : "Generate")}");
            ActivityLog.LogInformation("OllamaProvider", $"流式传输: {stream}");
            ActivityLog.LogInformation("OllamaProvider", $"提示词长度: {prompt?.Length ?? 0} 字符");
            ActivityLog.LogInformation("OllamaProvider", $"请求体大小: {json.Length} 字符");

            // 记录模型信息（安全地访问动态对象）
            try
            {
                using var tempDoc = JsonDocument.Parse(json);
                if (tempDoc.RootElement.TryGetProperty("model", out var modelElement))
                {
                    ActivityLog.LogInformation("OllamaProvider", $"模型: {modelElement.GetString()}");
                }
                if (tempDoc.RootElement.TryGetProperty("options", out var optionsElement))
                {
                    if (optionsElement.TryGetProperty("temperature", out var tempElement))
                    {
                        ActivityLog.LogInformation("OllamaProvider", $"温度: {tempElement.GetDouble()}");
                    }
                    if (optionsElement.TryGetProperty("num_predict", out var tokensElement))
                    {
                        ActivityLog.LogInformation("OllamaProvider", $"最大 Token: {tokensElement.GetInt32()}");
                    }
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogWarning("OllamaProvider", $"解析请求体日志失败: {ex.Message}");
            }

            try
            {
                if (stream && progress != null)
                {
                    return await SendStreamRequestAsync(apiUrl, content, progress, isChatFormat, cancellationToken).ConfigureAwait(false);
                }
                else
                {
                    return await SendNormalRequestAsync(apiUrl, content, isChatFormat, cancellationToken).ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("OllamaProvider", $"=== Ollama API 请求失败 ===");
                ActivityLog.LogError("OllamaProvider", $"错误类型: {ex.GetType().Name}");
                ActivityLog.LogError("OllamaProvider", $"错误消息: {ex.Message}");
                ActivityLog.LogError("OllamaProvider", $"请求 URL: {apiUrl}");
                ActivityLog.LogError("OllamaProvider", $"API 格式: {(isChatFormat ? "Chat" : "Generate")}");

                // 安全地记录模型信息
                try
                {
                    using var tempDoc = JsonDocument.Parse(json);
                    if (tempDoc.RootElement.TryGetProperty("model", out var modelElement))
                    {
                        ActivityLog.LogError("OllamaProvider", $"使用模型: {modelElement.GetString()}");
                    }
                }
                catch
                {
                    ActivityLog.LogError("OllamaProvider", "无法解析请求体中的模型信息");
                }

                if (ex.InnerException != null)
                {
                    ActivityLog.LogError("OllamaProvider", $"内部异常: {ex.InnerException.Message}");
                }

                throw;
            }
        }

        private async Task<string> SendNormalRequestAsync(string apiUrl, StringContent content, bool isChatFormat, CancellationToken cancellationToken)
        {
            ActivityLog.LogInformation("OllamaProvider", $"发送非流式请求 (格式: {(isChatFormat ? "chat" : "generate")})");

            using var response = await _httpClient.PostAsync(apiUrl, content, cancellationToken).ConfigureAwait(false);

            ActivityLog.LogInformation("OllamaProvider", $"收到响应 - 状态码: {response.StatusCode}");
            ActivityLog.LogInformation("OllamaProvider", $"响应头: {string.Join(", ", response.Headers.Select(h => $"{h.Key}={string.Join(",", h.Value)}"))}");

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                ActivityLog.LogError("OllamaProvider", $"HTTP 错误响应: {errorContent}");
                response.EnsureSuccessStatusCode(); // 这会抛出异常
            }

            var responseJson = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
            ActivityLog.LogInformation("OllamaProvider", $"响应内容长度: {responseJson.Length} 字符");

            using var document = JsonDocument.Parse(responseJson);

            string result = string.Empty;

            if (isChatFormat)
            {
                // Chat API 响应格式: {"message": {"role": "assistant", "content": "..."}}
                if (document.RootElement.TryGetProperty("message", out var messageElement) &&
                    messageElement.TryGetProperty("content", out var contentElement))
                {
                    result = contentElement.GetString() ?? string.Empty;
                    ActivityLog.LogInformation("OllamaProvider", $"=== Ollama Chat API 请求成功 ===");
                }
                else
                {
                    ActivityLog.LogWarning("OllamaProvider", "Chat API 响应中未找到 'message.content' 字段");
                }
            }
            else
            {
                // Generate API 响应格式: {"response": "..."}
                if (document.RootElement.TryGetProperty("response", out var responseElement))
                {
                    result = responseElement.GetString() ?? string.Empty;
                    ActivityLog.LogInformation("OllamaProvider", $"=== Ollama Generate API 请求成功 ===");
                }
                else
                {
                    ActivityLog.LogWarning("OllamaProvider", "Generate API 响应中未找到 'response' 字段");
                }
            }

            ActivityLog.LogInformation("OllamaProvider", $"响应长度: {result.Length} 字符");
            return result;
        }

        private async Task<string> SendStreamRequestAsync(string apiUrl, StringContent content, IProgress<string> progress, bool isChatFormat, CancellationToken cancellationToken)
        {
            ActivityLog.LogInformation("OllamaProvider", $"发送流式请求 (格式: {(isChatFormat ? "chat" : "generate")})");

            var request = new HttpRequestMessage(HttpMethod.Post, apiUrl) { Content = content };
            using var response = await _httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);

            ActivityLog.LogInformation("OllamaProvider", $"流式响应状态码: {response.StatusCode}");

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                ActivityLog.LogError("OllamaProvider", $"流式请求 HTTP 错误响应: {errorContent}");
                response.EnsureSuccessStatusCode(); // 这会抛出异常
            }

            var fullResponse = new StringBuilder();
            using var stream = await response.Content.ReadAsStreamAsync().ConfigureAwait(false);
            using var reader = new StreamReader(stream);

            ActivityLog.LogInformation("OllamaProvider", "开始读取流式响应");
            int lineCount = 0;

            string line;
            while ((line = await reader.ReadLineAsync().ConfigureAwait(false)) != null)
            {
                lineCount++;
                if (cancellationToken.IsCancellationRequested)
                {
                    ActivityLog.LogInformation("OllamaProvider", "流式请求被取消");
                    break;
                }

                try
                {
                    using var document = JsonDocument.Parse(line);
                    string chunk = null;

                    if (isChatFormat)
                    {
                        // Chat API 流式响应格式: {"message": {"role": "assistant", "content": "..."}}
                        if (document.RootElement.TryGetProperty("message", out var messageElement) &&
                            messageElement.TryGetProperty("content", out var contentElement))
                        {
                            chunk = contentElement.GetString();
                        }
                    }
                    else
                    {
                        // Generate API 流式响应格式: {"response": "..."}
                        if (document.RootElement.TryGetProperty("response", out var responseElement))
                        {
                            chunk = responseElement.GetString();
                        }
                    }

                    if (!string.IsNullOrEmpty(chunk))
                    {
                        fullResponse.Append(chunk);
                        progress?.Report(chunk);
                    }

                    // 检查是否完成
                    if (document.RootElement.TryGetProperty("done", out var doneElement) && doneElement.GetBoolean())
                    {
                        ActivityLog.LogInformation("OllamaProvider", $"流式响应完成，共处理 {lineCount} 行");
                        break;
                    }
                }
                catch (JsonException ex)
                {
                    ActivityLog.LogWarning("OllamaProvider", $"跳过无效的 JSON 行 {lineCount}: {ex.Message}");
                    ActivityLog.LogWarning("OllamaProvider", $"无效行内容: {line}");
                }
            }

            var result = fullResponse.ToString();
            ActivityLog.LogInformation("OllamaProvider", $"=== 流式请求完成 ===");
            ActivityLog.LogInformation("OllamaProvider", $"最终响应长度: {result.Length} 字符");
            return result;
        }

        private async Task<bool> IsOllamaRunningAsync()
        {
            try
            {
                var apiUrl = GetApiUrl(Constants.Ollama.TagsEndpoint);
                using var response = await _httpClient.GetAsync(apiUrl).ConfigureAwait(false);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("OllamaProvider", $"连接检测失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取可用的模型列表
        /// </summary>
        /// <returns>模型名称列表</returns>
        public async Task<List<string>> GetAvailableModelsAsync()
        {
            try
            {
                var apiUrl = GetApiUrl(Constants.Ollama.TagsEndpoint);
                ActivityLog.LogInformation("OllamaProvider", $"正在从 {apiUrl} 获取模型列表");

                using var response = await _httpClient.GetAsync(apiUrl).ConfigureAwait(false);

                if (!response.IsSuccessStatusCode)
                {
                    var errorMsg = $"HTTP {response.StatusCode}: {response.ReasonPhrase}";
                    ActivityLog.LogError("OllamaProvider", $"获取模型列表失败: {errorMsg}");

                    // 返回缓存的模型列表
                    return await _modelCache.GetCachedModelsAsync().ConfigureAwait(false);
                }

                var responseJson = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                using var document = JsonDocument.Parse(responseJson);

                var models = new List<string>();
                if (document.RootElement.TryGetProperty("models", out var modelsElement))
                {
                    foreach (var modelElement in modelsElement.EnumerateArray())
                    {
                        if (modelElement.TryGetProperty("name", out var nameElement))
                        {
                            var modelName = nameElement.GetString();
                            if (!string.IsNullOrEmpty(modelName))
                            {
                                models.Add(modelName);
                            }
                        }
                    }
                }

                if (models.Any())
                {
                    // 缓存成功获取的模型列表
                    await _modelCache.SaveModelsAsync(models).ConfigureAwait(false);
                    ActivityLog.LogInformation("OllamaProvider", $"成功获取 {models.Count} 个模型");
                    return models;
                }
                else
                {
                    ActivityLog.LogWarning("OllamaProvider", "未找到任何模型");
                    return await _modelCache.GetCachedModelsAsync().ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("OllamaProvider", $"获取模型列表异常: {ex.Message}");
                // 返回缓存的模型列表
                return await _modelCache.GetCachedModelsAsync().ConfigureAwait(false);
            }
        }

        /// <summary>
        /// 测试连接并返回详细诊断信息
        /// </summary>
        /// <returns>连接测试结果</returns>
        public async Task<ConnectionTestResult> TestConnectionAsync()
        {
            var result = new ConnectionTestResult();

            try
            {
                var apiUrl = GetApiUrl(Constants.Ollama.TagsEndpoint);
                result.TestUrl = apiUrl;

                ActivityLog.LogInformation("OllamaProvider", $"开始连接测试: {apiUrl}");

                using var response = await _httpClient.GetAsync(apiUrl).ConfigureAwait(false);

                result.IsSuccess = response.IsSuccessStatusCode;
                result.StatusCode = (int)response.StatusCode;
                result.StatusMessage = response.ReasonPhrase;

                if (result.IsSuccess)
                {
                    var responseContent = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                    using var document = JsonDocument.Parse(responseContent);

                    if (document.RootElement.TryGetProperty("models", out var modelsElement))
                    {
                        result.AvailableModelsCount = modelsElement.GetArrayLength();
                        result.Message = $"连接成功！发现 {result.AvailableModelsCount} 个可用模型";
                    }
                    else
                    {
                        result.Message = "连接成功，但未找到模型信息";
                    }

                    ActivityLog.LogInformation("OllamaProvider", result.Message);
                }
                else
                {
                    result.Message = $"连接失败: HTTP {result.StatusCode} - {result.StatusMessage}";
                    ActivityLog.LogError("OllamaProvider", result.Message);
                }
            }
            catch (HttpRequestException httpEx)
            {
                result.Message = $"网络连接失败: {httpEx.Message}。请检查 Ollama 服务是否运行以及 URL 是否正确";
                ActivityLog.LogError("OllamaProvider", $"HTTP请求异常: {httpEx.Message}");
            }
            catch (TaskCanceledException timeoutEx)
            {
                result.Message = $"连接超时: {timeoutEx.Message}。请检查网络连接或增加超时时间";
                ActivityLog.LogError("OllamaProvider", $"连接超时: {timeoutEx.Message}");
            }
            catch (Exception ex)
            {
                result.Message = $"连接测试失败: {ex.Message}";
                ActivityLog.LogError("OllamaProvider", $"连接测试异常: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 检查是否是 messages 格式的 JSON
        /// </summary>
        /// <param name="prompt">输入的提示词</param>
        /// <returns>是否是 messages 格式</returns>
        private bool IsMessagesFormat(string prompt)
        {
            try
            {
                if (string.IsNullOrEmpty(prompt) || !prompt.TrimStart().StartsWith("{"))
                    return false;

                using var document = JsonDocument.Parse(prompt);
                return document.RootElement.TryGetProperty("messages", out _);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 创建聊天请求体
        /// </summary>
        /// <param name="messagesJson">messages 格式的 JSON</param>
        /// <param name="stream">是否流式传输</param>
        /// <returns>请求体对象</returns>
        private object CreateChatRequestBody(string messagesJson, bool stream)
        {
            try
            {
                using var document = JsonDocument.Parse(messagesJson);
                var messagesElement = document.RootElement.GetProperty("messages");

                var messages = new List<object>();
                foreach (var messageElement in messagesElement.EnumerateArray())
                {
                    var role = messageElement.GetProperty("role").GetString();
                    var content = messageElement.GetProperty("content").GetString();
                    messages.Add(new { role, content });
                }

                return new
                {
                    model = string.IsNullOrEmpty(_options.Model) ? Constants.Defaults.DefaultOllamaModel : _options.Model,
                    messages = messages,
                    stream = stream,
                    options = new
                    {
                        temperature = _options.Temperature,
                        num_predict = _options.MaxTokens
                    }
                };
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("OllamaProvider", $"解析 messages 格式失败: {ex.Message}");
                // 回退到简单格式
                return new
                {
                    model = string.IsNullOrEmpty(_options.Model) ? Constants.Defaults.DefaultOllamaModel : _options.Model,
                    prompt = messagesJson,
                    stream = stream,
                    options = new
                    {
                        temperature = _options.Temperature,
                        num_predict = _options.MaxTokens
                    }
                };
            }
        }

        /// <summary>
        /// 获取 API URL
        /// </summary>
        /// <param name="endpoint">端点路径</param>
        /// <returns>完整的 API URL</returns>
        private string GetApiUrl(string endpoint)
        {
            var baseUrl = string.IsNullOrEmpty(_options.ApiBase)
                ? Constants.Ollama.DefaultApiBase
                : _options.ApiBase.TrimEnd('/');

            return $"{baseUrl}{endpoint}";
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    /// <summary>
    /// 连接测试结果
    /// </summary>
    public class ConnectionTestResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
        public string TestUrl { get; set; } = string.Empty;
        public int StatusCode { get; set; }
        public string StatusMessage { get; set; } = string.Empty;
        public int AvailableModelsCount { get; set; }
    }
}
