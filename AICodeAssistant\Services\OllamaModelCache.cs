using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.VisualStudio.Shell;

namespace AICodeAssistant.Services
{
    /// <summary>
    /// Ollama 模型缓存管理器
    /// </summary>
    public class OllamaModelCache
    {
        private readonly string _cacheFilePath;

        public OllamaModelCache()
        {
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                if (string.IsNullOrEmpty(appDataPath))
                {
                    ActivityLog.LogError("OllamaModelCache", "无法获取 AppData 路径");
                    _cacheFilePath = string.Empty;
                    return;
                }

                var folderPath = Path.Combine(appDataPath, Constants.Config.AppDataFolder);
                _cacheFilePath = Path.Combine(folderPath, Constants.Config.ModelCacheFileName);

                ActivityLog.LogInformation("OllamaModelCache", $"初始化缓存路径: {_cacheFilePath}");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("OllamaModelCache", $"初始化缓存路径失败: {ex.Message}");
                _cacheFilePath = string.Empty;
            }
        }

        /// <summary>
        /// 获取缓存的模型列表
        /// </summary>
        /// <returns>模型名称列表</returns>
        public async Task<List<string>> GetCachedModelsAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(_cacheFilePath))
                {
                    ActivityLog.LogWarning("OllamaModelCache", "缓存文件路径为空，返回默认模型");
                    return new List<string> { Constants.Defaults.DefaultOllamaModel };
                }

                if (!File.Exists(_cacheFilePath))
                {
                    ActivityLog.LogInformation("OllamaModelCache", $"缓存文件不存在: {_cacheFilePath}，返回默认模型");
                    return new List<string> { Constants.Defaults.DefaultOllamaModel };
                }

                var json = await Task.Run(() => File.ReadAllText(_cacheFilePath, Encoding.UTF8)).ConfigureAwait(false);

                if (string.IsNullOrWhiteSpace(json))
                {
                    ActivityLog.LogWarning("OllamaModelCache", "缓存文件内容为空，返回默认模型");
                    return new List<string> { Constants.Defaults.DefaultOllamaModel };
                }

                var cacheData = JsonSerializer.Deserialize<ModelCacheData>(json);

                // 检查缓存是否过期（24小时）
                if (cacheData != null &&
                    DateTime.UtcNow - cacheData.LastUpdated < TimeSpan.FromHours(24) &&
                    cacheData.Models?.Any() == true)
                {
                    ActivityLog.LogInformation("OllamaModelCache", $"从缓存加载了 {cacheData.Models.Count} 个模型");
                    return cacheData.Models;
                }

                if (cacheData != null)
                {
                    var age = DateTime.UtcNow - cacheData.LastUpdated;
                    ActivityLog.LogInformation("OllamaModelCache", $"缓存已过期（{age.TotalHours:F1} 小时），返回默认模型");
                }

                return new List<string> { Constants.Defaults.DefaultOllamaModel };
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("OllamaModelCache", $"读取模型缓存失败: {ex.Message}");
                ActivityLog.LogError("OllamaModelCache", $"完整错误信息: {ex}");
                return new List<string> { Constants.Defaults.DefaultOllamaModel };
            }
        }

        /// <summary>
        /// 保存模型列表到缓存
        /// </summary>
        /// <param name="models">模型名称列表</param>
        public async Task SaveModelsAsync(List<string> models)
        {
            try
            {
                if (models == null || !models.Any())
                {
                    ActivityLog.LogInformation("OllamaModelCache", "模型列表为空，跳过缓存保存");
                    return;
                }

                // 检查缓存是否可用
                if (!IsCacheAvailable())
                {
                    ActivityLog.LogWarning("OllamaModelCache", "缓存不可用，跳过保存");
                    return;
                }

                var folderPath = Path.GetDirectoryName(_cacheFilePath);
                ActivityLog.LogInformation("OllamaModelCache", $"缓存文件夹路径: {folderPath}");
                ActivityLog.LogInformation("OllamaModelCache", $"缓存文件路径: {_cacheFilePath}");

                var cacheData = new ModelCacheData
                {
                    Models = models.Distinct().ToList(),
                    LastUpdated = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(cacheData, new JsonSerializerOptions
                {
                    WriteIndented = true
                });

                await Task.Run(() =>
                {
                    try
                    {
                        // 先写入临时文件，然后重命名，避免写入过程中文件损坏
                        var tempFilePath = _cacheFilePath + ".tmp";
                        File.WriteAllText(tempFilePath, json, Encoding.UTF8);

                        // 如果目标文件存在，先删除
                        if (File.Exists(_cacheFilePath))
                        {
                            File.Delete(_cacheFilePath);
                        }

                        // 重命名临时文件为目标文件
                        File.Move(tempFilePath, _cacheFilePath);
                    }
                    catch (Exception innerEx)
                    {
                        ActivityLog.LogError("OllamaModelCache", $"写入缓存文件时发生错误: {innerEx.Message}");
                        ActivityLog.LogError("OllamaModelCache", $"错误详情: {innerEx}");
                        throw;
                    }
                }).ConfigureAwait(false);

                ActivityLog.LogInformation("OllamaModelCache", $"已成功缓存 {models.Count} 个模型到 {_cacheFilePath}");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("OllamaModelCache", $"保存模型缓存失败: {ex.Message}");
                ActivityLog.LogError("OllamaModelCache", $"完整错误信息: {ex}");
                // 不重新抛出异常，避免影响主流程
            }
        }

        /// <summary>
        /// 清除缓存
        /// </summary>
        public async Task ClearCacheAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    if (File.Exists(_cacheFilePath))
                    {
                        File.Delete(_cacheFilePath);
                        ActivityLog.LogInformation("OllamaModelCache", "模型缓存已清除");
                    }
                }).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("OllamaModelCache", $"清除模型缓存失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取缓存文件路径（用于调试）
        /// </summary>
        public string GetCacheFilePath()
        {
            return _cacheFilePath;
        }

        /// <summary>
        /// 检查缓存是否可用
        /// </summary>
        public bool IsCacheAvailable()
        {
            try
            {
                if (string.IsNullOrEmpty(_cacheFilePath))
                {
                    return false;
                }

                var folderPath = Path.GetDirectoryName(_cacheFilePath);
                if (string.IsNullOrEmpty(folderPath))
                {
                    return false;
                }

                // 尝试创建目录（如果不存在）
                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }

                // 尝试写入测试文件
                var testFilePath = Path.Combine(folderPath, "test.tmp");
                File.WriteAllText(testFilePath, "test");
                File.Delete(testFilePath);

                return true;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("OllamaModelCache", $"缓存不可用: {ex.Message}");
                return false;
            }
        }
    }

    /// <summary>
    /// 模型缓存数据结构
    /// </summary>
    internal class ModelCacheData
    {
        public List<string> Models { get; set; } = new List<string>();
        public DateTime LastUpdated { get; set; }
    }
}
