using System;
using System.ComponentModel.Composition;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.VisualStudio.Language.Intellisense.AsyncCompletion;
using Microsoft.VisualStudio.Shell;
using Microsoft.VisualStudio.Text;
using Microsoft.VisualStudio.Text.Editor;
using Microsoft.VisualStudio.Utilities;
using AICodeAssistant.Services;
using AICodeAssistant.UI;

namespace AICodeAssistant.Features.Completion
{
    /// <summary>
    /// AI 代码补全源提供者
    /// </summary>
    // Disabled to avoid interference with Ghost Text
    // [Export(typeof(IAsyncCompletionSourceProvider))]
    [Name("AI Code Completion")]
    [ContentType("code")]
    [ContentType("text")]
    public class AICompletionSourceProvider : IAsyncCompletionSourceProvider
    {
        public IAsyncCompletionSource GetOrCreate(ITextView textView)
        {
            try
            {
                ActivityLog.LogInformation("AICompletionSourceProvider", "开始创建补全源");

                // 检查是否已经为此文本视图创建了补全源
                if (textView.Properties.TryGetProperty(typeof(AICompletionSource), out AICompletionSource existingSource))
                {
                    ActivityLog.LogInformation("AICompletionSourceProvider", "返回现有的补全源");
                    return existingSource;
                }

                // 获取文件信息用于调试
                var filePath = GetFilePath(textView);
                ActivityLog.LogInformation("AICompletionSourceProvider", $"为文件创建补全源: {filePath}");

                // 获取设置
                var settings = LoadSettings();
                if (settings == null)
                {
                    ActivityLog.LogWarning("AICompletionSourceProvider", "无法加载设置，跳过补全");
                    return null;
                }

                ActivityLog.LogInformation("AICompletionSourceProvider", $"补全功能启用状态: {settings.Completion.Enabled}");

                // 获取 LLM 提供者
                var llmProvider = GetLlmProvider();
                if (llmProvider == null)
                {
                    ActivityLog.LogWarning("AICompletionSourceProvider", "LLM 提供者为空，跳过补全");
                    return null;
                }

                if (!llmProvider.IsAvailable)
                {
                    ActivityLog.LogWarning("AICompletionSourceProvider", $"LLM 提供者 {llmProvider.Name} 不可用，跳过补全");
                    return null;
                }

                ActivityLog.LogInformation("AICompletionSourceProvider", $"使用 LLM 提供者: {llmProvider.Name}");

                // 创建新的补全源
                var completionSource = new AICompletionSource(textView, llmProvider, settings.Completion);
                textView.Properties.AddProperty(typeof(AICompletionSource), completionSource);

                ActivityLog.LogInformation("AICompletionSourceProvider", "补全源创建成功");
                return completionSource;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AICompletionSourceProvider", $"创建补全源失败: {ex.Message}");
                return null;
            }
        }

        private ILlmProvider GetLlmProvider()
        {
            try
            {
                // 从设置文件中读取配置
                var settings = LoadSettings();
                if (settings == null || !settings.Completion.Enabled)
                    return null;

                var options = CreateLlmOptions(settings);
                return LlmProviderFactory.CreateProvider(settings.ProviderType, options);
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AICompletionSourceProvider", $"获取 LLM 提供者失败: {ex.Message}");
                return null;
            }
        }

        private AppSettings LoadSettings()
        {
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var folderPath = Path.Combine(appDataPath, Constants.Config.AppDataFolder);
                var filePath = Path.Combine(folderPath, Constants.Config.SettingsFileName);

                if (File.Exists(filePath))
                {
                    var json = File.ReadAllText(filePath);
                    return JsonSerializer.Deserialize<AppSettings>(json) ?? AppSettings.CreateDefault();
                }
                else
                {
                    return AppSettings.CreateDefault();
                }
            }
            catch
            {
                return AppSettings.CreateDefault();
            }
        }

        private LlmOptions CreateLlmOptions(AppSettings settings)
        {
            return settings.ProviderType switch
            {
                LlmProviderType.OpenAI => new LlmOptions
                {
                    ApiKey = settings.OpenAI.ApiKey,
                    ApiBase = settings.OpenAI.ApiBase,
                    Model = settings.OpenAI.Model,
                    Temperature = settings.Completion.Temperature,
                    MaxTokens = settings.Completion.MaxTokens,
                    TimeoutSeconds = 30
                },
                LlmProviderType.Ollama => new LlmOptions
                {
                    ApiBase = settings.Ollama.ApiBase,
                    Model = settings.Ollama.Model,
                    Temperature = settings.Completion.Temperature,
                    MaxTokens = settings.Completion.MaxTokens,
                    TimeoutSeconds = 60
                },
                _ => LlmProviderFactory.GetDefaultOptions(LlmProviderType.Ollama)
            };
        }

        private string GetFilePath(ITextView textView)
        {
            try
            {
                if (textView.TextBuffer.Properties.TryGetProperty(typeof(ITextDocument), out ITextDocument document))
                {
                    return document.FilePath ?? "Unknown";
                }
            }
            catch
            {
                // 忽略异常
            }

            return "Unknown";
        }
    }
}
