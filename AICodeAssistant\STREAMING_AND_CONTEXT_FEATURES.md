# 流式输出和上下文过滤功能

## 功能概述

本次更新实现了两个重要功能：

### 1. 流式输出（去掉"正在生成"显示）

**实现原理：**
- 移除了 `Constants.UI.LoadingText`（"正在生成..."）常量
- 修改消息显示逻辑，直接创建空的助手消息容器
- 通过 `UpdateTypingMessage` 方法实时追加流式文本块
- 使用 `FinalizeStreamingMessage` 完成流式输出并保存到历史记录

**核心方法：**
- `UpdateTypingMessage(string chunk)` - 实时更新流式内容
- `FinalizeStreamingMessage(string finalContent)` - 完成流式输出
- `ClearStreamingMessage()` - 清除流式消息（用于取消或错误情况）

**用户体验改进：**
- 用户可以实时看到AI的回复内容逐步生成
- 不再显示"正在生成..."的等待提示
- 提供更自然的对话体验

### 2. 上下文信息过滤（移除思考过程）

**实现原理：**
- 在 `BuildContextHistory` 方法中添加内容过滤逻辑
- 使用正则表达式识别并移除 `<think>...</think>` 标签及其内容
- 保留正文内容用于上下文传递

**核心方法：**
- `FilterThinkingContent(string content)` - 过滤思考过程内容
- 支持多行和嵌套的 `<think>` 标签
- 自动清理多余的空行和空白字符

**过滤规则：**
```regex
<think\s*>.*?</think\s*>
```

**示例：**
```
输入：
这是正文内容。

<think>
这是AI的思考过程，
包含推理步骤。
</think>

这是回复的主要内容。

输出：
这是正文内容。

这是回复的主要内容。
```

### 3. 回车键发送消息

**已实现功能：**
- 单独按 `Enter` 键发送消息
- `Ctrl+Enter` 插入换行符
- `Escape` 键退出编辑模式

**键盘快捷键：**
- `Enter` - 发送消息
- `Ctrl+Enter` - 插入换行
- `Escape` - 退出编辑模式（在编辑状态下）

## 技术实现细节

### 流式输出架构

1. **消息创建**：创建带有 `IsTyping = true` 标记的空消息
2. **内容更新**：通过 `UpdateTypingMessage` 逐步追加内容
3. **完成处理**：使用 `FinalizeStreamingMessage` 移除标记并保存历史

### 上下文过滤架构

1. **历史遍历**：从最新消息向前遍历聊天历史
2. **内容过滤**：对每条消息应用 `FilterThinkingContent`
3. **Token 计算**：基于过滤后的内容计算 Token 使用量
4. **上下文构建**：组装最终的上下文字符串

### 错误处理

- 过滤失败时返回原始内容，确保功能稳定性
- 流式输出异常时自动清理临时消息
- 详细的日志记录便于问题诊断

## 配置选项

用户可以通过设置页面配置：
- 最大上下文长度（Token 数）
- 是否启用上下文历史
- 其他相关参数

## 测试

包含单元测试验证：
- 思考内容过滤的正确性
- 多种边界情况的处理
- 错误情况的容错性

## 兼容性

- 保持与现有功能的完全兼容
- 向后兼容旧的消息格式
- 渐进式功能增强，不影响现有用户体验
