using System;
using System.ComponentModel.Composition;
using System.Windows.Input;
using System.Windows.Media;
using Microsoft.VisualStudio.Text;
using Microsoft.VisualStudio.Text.Editor;
using Microsoft.VisualStudio.Text.Formatting;
using Microsoft.VisualStudio.Utilities;
using Microsoft.VisualStudio.Shell;
using System.Threading.Tasks;
using System.Threading;

namespace AICodeAssistant.Features.Completion
{
    /// <summary>
    /// 内联补全控制器，处理灰色文本显示和Tab键接受
    /// </summary>
    // Disabled to avoid interference with Pure Ghost Text
    // [Export(typeof(IWpfTextViewCreationListener))]
    [ContentType("code")]
    [ContentType("text")]
    [TextViewRole(PredefinedTextViewRoles.Editable)]
    public class InlineCompletionController : IWpfTextViewCreationListener
    {
        public void TextViewCreated(IWpfTextView textView)
        {
            try
            {
                // 为每个文本视图创建内联补全管理器
                var manager = new InlineCompletionManager(textView);
                textView.Properties.AddProperty(typeof(InlineCompletionManager), manager);

                ActivityLog.LogInformation("InlineCompletionController", "为文本视图创建内联补全管理器");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineCompletionController", $"创建内联补全管理器失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 内联补全管理器，管理单个文本视图的内联补全功能
    /// </summary>
    public class InlineCompletionManager : IDisposable
    {
        private readonly IWpfTextView _textView;
        private readonly AIInlineCompletionProvider _completionProvider;
        
        private IAdornmentLayer _adornmentLayer;
        private string _currentSuggestion;
        private SnapshotPoint? _suggestionPosition;
        private CancellationTokenSource _currentRequest;
        
        // 延迟触发设置
        private Timer _triggerTimer;
        private const int TriggerDelayMs = 1000; // 1秒延迟

        public InlineCompletionManager(IWpfTextView textView)
        {
            _textView = textView ?? throw new ArgumentNullException(nameof(textView));
            _completionProvider = new AIInlineCompletionProvider();

            // 获取装饰层
            _adornmentLayer = _textView.GetAdornmentLayer("InlineCompletion");

            // 订阅事件
            _textView.TextBuffer.Changed += OnTextBufferChanged;
            _textView.Caret.PositionChanged += OnCaretPositionChanged;
            _textView.KeyDown += OnKeyDown;
            _textView.Closed += OnTextViewClosed;

            ActivityLog.LogInformation("InlineCompletionManager", "内联补全管理器初始化完成");
        }

        /// <summary>
        /// 文本缓冲区变化事件
        /// </summary>
        private void OnTextBufferChanged(object sender, TextContentChangedEventArgs e)
        {
            try
            {
                // 清除当前建议
                ClearSuggestion();

                // 如果是用户输入，延迟触发补全
                if (e.Changes.Count > 0)
                {
                    var change = e.Changes[0];
                    if (!string.IsNullOrEmpty(change.NewText) && change.OldText.Length == 0)
                    {
                        // 这是插入操作，延迟触发补全
                        ScheduleTriggerCompletion();
                    }
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineCompletionManager", $"处理文本变化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 光标位置变化事件
        /// </summary>
        private void OnCaretPositionChanged(object sender, CaretPositionChangedEventArgs e)
        {
            try
            {
                // 如果光标移动，清除建议
                if (_suggestionPosition.HasValue && 
                    e.NewPosition.BufferPosition != _suggestionPosition.Value)
                {
                    ClearSuggestion();
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineCompletionManager", $"处理光标变化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 键盘按键事件
        /// </summary>
        private void OnKeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(_currentSuggestion))
                    return;

                if (e.Key == Key.Tab)
                {
                    // Tab键接受建议
                    AcceptSuggestion();
                    e.Handled = true;
                }
                else if (e.Key == Key.Escape)
                {
                    // Esc键拒绝建议
                    ClearSuggestion();
                    e.Handled = true;
                }
                else if (e.Key == Key.Left || e.Key == Key.Right || 
                         e.Key == Key.Up || e.Key == Key.Down ||
                         e.Key == Key.Home || e.Key == Key.End)
                {
                    // 方向键清除建议
                    ClearSuggestion();
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineCompletionManager", $"处理按键失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 文本视图关闭事件
        /// </summary>
        private void OnTextViewClosed(object sender, EventArgs e)
        {
            Dispose();
        }

        /// <summary>
        /// 安排触发补全（延迟执行）
        /// </summary>
        private void ScheduleTriggerCompletion()
        {
            try
            {
                // 取消之前的定时器
                _triggerTimer?.Dispose();

                // 创建新的定时器
                _triggerTimer = new Timer(async _ =>
                {
                    try
                    {
                        await TriggerCompletionAsync();
                    }
                    catch (Exception ex)
                    {
                        ActivityLog.LogError("InlineCompletionManager", $"触发补全失败: {ex.Message}");
                    }
                }, null, TriggerDelayMs, Timeout.Infinite);
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineCompletionManager", $"安排触发补全失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 触发补全
        /// </summary>
        private async Task TriggerCompletionAsync()
        {
            try
            {
                // 取消之前的请求
                _currentRequest?.Cancel();
                _currentRequest = new CancellationTokenSource();

                var caretPosition = _textView.Caret.Position.BufferPosition;
                
                // 获取补全建议
                var result = await _completionProvider.GetCompletionAsync(
                    _textView, caretPosition, _currentRequest.Token);

                if (result != null && !string.IsNullOrEmpty(result.Text))
                {
                    // 在UI线程上显示建议
                    await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                    ShowSuggestion(result.Text, caretPosition);
                }
            }
            catch (OperationCanceledException)
            {
                // 请求被取消，忽略
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineCompletionManager", $"触发补全异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示建议（灰色文本）
        /// </summary>
        private void ShowSuggestion(string suggestion, SnapshotPoint position)
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                // 清除之前的建议
                ClearSuggestion();

                _currentSuggestion = suggestion;
                _suggestionPosition = position;

                // 创建灰色文本装饰
                var span = new SnapshotSpan(position, 0);
                var geometry = _textView.TextViewLines.GetMarkerGeometry(span);
                
                if (geometry != null)
                {
                    var textBlock = new System.Windows.Controls.TextBlock
                    {
                        Text = suggestion,
                        Foreground = new SolidColorBrush(Colors.Gray),
                        Opacity = 0.6,
                        FontFamily = _textView.FormattedLineSource.DefaultTextProperties.Typeface.FontFamily,
                        FontSize = _textView.FormattedLineSource.DefaultTextProperties.FontRenderingEmSize
                    };

                    // 添加到装饰层
                    _adornmentLayer.AddAdornment(
                        AdornmentPositioningBehavior.TextRelative,
                        span,
                        "InlineCompletion",
                        textBlock,
                        null);

                    ActivityLog.LogInformation("InlineCompletionManager", $"显示内联建议: '{suggestion}'");
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineCompletionManager", $"显示建议失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 接受建议
        /// </summary>
        private void AcceptSuggestion()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                if (string.IsNullOrEmpty(_currentSuggestion) || !_suggestionPosition.HasValue)
                    return;

                // 插入建议文本
                var edit = _textView.TextBuffer.CreateEdit();
                edit.Insert(_suggestionPosition.Value.Position, _currentSuggestion);
                edit.Apply();

                ActivityLog.LogInformation("InlineCompletionManager", $"接受内联建议: '{_currentSuggestion}'");

                // 清除建议
                ClearSuggestion();
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineCompletionManager", $"接受建议失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除建议
        /// </summary>
        private void ClearSuggestion()
        {
            try
            {
                if (!string.IsNullOrEmpty(_currentSuggestion))
                {
                    ThreadHelper.ThrowIfNotOnUIThread();
                    _adornmentLayer.RemoveAllAdornments();
                    _currentSuggestion = null;
                    _suggestionPosition = null;
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineCompletionManager", $"清除建议失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                // 取消当前请求
                _currentRequest?.Cancel();
                _currentRequest?.Dispose();

                // 停止定时器
                _triggerTimer?.Dispose();

                // 清除建议
                ClearSuggestion();

                // 取消事件订阅
                if (_textView != null)
                {
                    _textView.TextBuffer.Changed -= OnTextBufferChanged;
                    _textView.Caret.PositionChanged -= OnCaretPositionChanged;
                    _textView.KeyDown -= OnKeyDown;
                    _textView.Closed -= OnTextViewClosed;
                }

                // 释放提供者
                _completionProvider?.Dispose();

                ActivityLog.LogInformation("InlineCompletionManager", "内联补全管理器已释放");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineCompletionManager", $"释放资源失败: {ex.Message}");
            }
        }
    }
}
