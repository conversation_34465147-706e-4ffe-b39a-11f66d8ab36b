using System;
using System.Threading;
using System.Threading.Tasks;

namespace AICodeAssistant.Services
{
    /// <summary>
    /// LLM 提供者接口，支持流式和非流式响应
    /// </summary>
    public interface ILlmProvider
    {
        /// <summary>
        /// 发送提示词到 LLM 并获取响应
        /// </summary>
        /// <param name="prompt">提示词</param>
        /// <param name="stream">是否使用流式响应</param>
        /// <param name="progress">流式响应进度回调</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>完整响应文本</returns>
        Task<string> SendAsync(string prompt, bool stream = false, IProgress<string> progress = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 提供者名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 是否可用
        /// </summary>
        bool IsAvailable { get; }
    }

    /// <summary>
    /// LLM 配置选项
    /// </summary>
    public class LlmOptions
    {
        public string ApiKey { get; set; } = string.Empty;
        public string ApiBase { get; set; } = string.Empty;
        public string Model { get; set; } = string.Empty;
        public double Temperature { get; set; } = 0.7;
        public int MaxTokens { get; set; } = 2048;
        public int TimeoutSeconds { get; set; } = 30;
    }

    /// <summary>
    /// LLM 提供者类型
    /// </summary>
    public enum LlmProviderType
    {
        OpenAI,
        Ollama
    }
}
