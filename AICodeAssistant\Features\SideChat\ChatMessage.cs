using System;
using System.Text.Json.Serialization;

namespace AICodeAssistant.Features.SideChat
{
    /// <summary>
    /// 聊天消息模型
    /// </summary>
    public class ChatMessage
    {
        /// <summary>
        /// 消息角色 (user, assistant, system)
        /// </summary>
        [JsonPropertyName("role")]
        public string Role { get; set; } = string.Empty;

        /// <summary>
        /// 消息内容
        /// </summary>
        [JsonPropertyName("content")]
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 时间戳
        /// </summary>
        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否为正在输入状态
        /// </summary>
        [JsonIgnore]
        public bool IsTyping { get; set; } = false;

        /// <summary>
        /// 是否为错误消息
        /// </summary>
        [JsonPropertyName("isError")]
        public bool IsError { get; set; } = false;

        /// <summary>
        /// 消息ID（用于引用和编辑）
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 关联的文件路径
        /// </summary>
        [JsonPropertyName("filePath")]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 关联的代码片段
        /// </summary>
        [JsonPropertyName("codeSnippet")]
        public string CodeSnippet { get; set; } = string.Empty;
    }
}
