# AI 代码补全功能故障排除指南

## 问题：补全功能未被调用

如果您发现 AI 代码补全功能没有被触发，请按照以下步骤进行排查：

## 🔍 快速诊断步骤

### 1. 检查基本设置

**步骤：**
1. 打开 Visual Studio
2. 转到 `扩展` → `AI Code Assistant 设置`
3. 确认以下设置：
   - ✅ `启用 AI 代码补全` 已勾选
   - ✅ 上下文行数 > 0（建议 10-20）
   - ✅ 最大 Token 数 > 0（建议 1024-2048）

### 2. 检查 LLM 提供者配置

**对于 Ollama 用户：**
1. 确认 Ollama 服务正在运行
   ```bash
   # 检查 Ollama 是否运行
   curl http://localhost:11434/api/tags
   ```
2. 确认模型已下载
   ```bash
   # 下载 codellama 模型（如果未下载）
   ollama pull codellama
   ```
3. 检查 API 地址设置（默认：`http://localhost:11434`）

**对于 OpenAI 用户：**
1. 确认 API 密钥已正确设置
2. 检查 API 地址（默认：`https://api.openai.com`）
3. 确认模型名称正确（如：`gpt-3.5-turbo`）

### 3. 检查文件类型支持

确认您正在编辑的文件类型在支持列表中：
- ✅ C# (.cs)
- ✅ VB.NET (.vb)
- ✅ C++ (.cpp, .c, .h, .hpp)
- ✅ JavaScript (.js)
- ✅ TypeScript (.ts)
- ✅ Python (.py)
- ✅ Java (.java)
- ✅ XML/XAML (.xml, .xaml)
- ✅ JSON (.json)
- ✅ HTML (.html)
- ✅ CSS (.css)
- ✅ SQL (.sql)

## 🔧 详细排查步骤

### 步骤 1：查看活动日志

1. 在 Visual Studio 中，转到 `帮助` → `发送反馈` → `报告问题`
2. 点击 `查看详细信息`
3. 查找以下关键词的日志条目：
   - `AICompletionSourceProvider`
   - `AICompletionSource`
   - `CompletionDiagnostics`

**正常日志示例：**
```
AICompletionSourceProvider: 开始创建补全源
AICompletionSourceProvider: 为文件创建补全源: TestFile.cs
AICompletionSourceProvider: 补全功能启用状态: True
AICompletionSourceProvider: 使用 LLM 提供者: Ollama
AICompletionSourceProvider: 补全源创建成功
AICompletionSource: InitializeCompletion 被调用
```

**问题日志示例：**
```
AICompletionSourceProvider: LLM 提供者为空，跳过补全
AICompletionSourceProvider: LLM 提供者 Ollama 不可用，跳过补全
AICompletionSourceProvider: 无法加载设置，跳过补全
```

### 步骤 2：手动触发补全

1. 在支持的文件类型中（如 .cs 文件）
2. 输入一些代码，例如：
   ```csharp
   public class Test
   {
       public void Method()
       {
           var result = 
   ```
3. 尝试以下触发方式：
   - 输入 `.` 字符
   - 输入 `(` 字符
   - 按 `Ctrl+Space`

### 步骤 3：检查网络连接

**对于 Ollama：**
```bash
# 测试本地连接
curl -X POST http://localhost:11434/api/generate -d '{
  "model": "codellama",
  "prompt": "Hello",
  "stream": false
}'
```

**对于 OpenAI：**
```bash
# 测试 API 连接（替换 YOUR_API_KEY）
curl -X POST https://api.openai.com/v1/chat/completions \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 10
  }'
```

### 步骤 4：重置配置

如果以上步骤都无效，尝试重置配置：

1. 关闭 Visual Studio
2. 删除设置文件：
   ```
   %AppData%\OllamaAICodeAssistant\settings.json
   ```
3. 重新启动 Visual Studio
4. 重新配置设置

## 🚨 常见问题和解决方案

### 问题 1：补全源未被创建

**症状：** 活动日志中没有 `AICompletionSourceProvider` 相关信息

**可能原因：**
- MEF 导出未正确注册
- 扩展未正确安装

**解决方案：**
1. 重新安装扩展
2. 重启 Visual Studio
3. 检查扩展是否在 `扩展` → `管理扩展` 中显示为已启用

### 问题 2：LLM 提供者不可用

**症状：** 日志显示 "LLM 提供者不可用"

**Ollama 解决方案：**
```bash
# 启动 Ollama 服务
ollama serve

# 在另一个终端中下载模型
ollama pull codellama
```

**OpenAI 解决方案：**
1. 检查 API 密钥是否正确
2. 检查网络连接
3. 验证账户余额

### 问题 3：补全功能被禁用

**症状：** 日志显示 "补全功能启用状态: False"

**解决方案：**
1. 打开设置页面
2. 勾选 `启用 AI 代码补全`
3. 保存设置

### 问题 4：触发条件不满足

**症状：** 日志显示 "不参与补全"

**解决方案：**
1. 确保输入了足够的字符（至少 2 个）
2. 尝试输入触发字符：`.` `(` `[` `<`
3. 使用 `Ctrl+Space` 手动触发

## 📊 性能优化建议

如果补全功能工作但响应缓慢：

1. **减少上下文行数**：从 20 行减少到 10-15 行
2. **降低最大 Token 数**：从 2048 减少到 1024
3. **调整温度参数**：使用较低的值（0.3-0.5）
4. **使用本地模型**：Ollama 通常比 API 调用更快

## 🔄 重启和重新安装

如果所有方法都无效：

1. **软重启：**
   - 关闭所有 Visual Studio 实例
   - 重新启动 Visual Studio

2. **硬重启：**
   - 重启计算机
   - 重新启动 Ollama 服务（如果使用）

3. **重新安装扩展：**
   - 卸载扩展
   - 重启 Visual Studio
   - 重新安装扩展

## 📞 获取帮助

如果问题仍然存在：

1. **收集日志信息：**
   - Visual Studio 活动日志
   - 设置文件内容
   - 错误消息截图

2. **提供环境信息：**
   - Visual Studio 版本
   - 操作系统版本
   - 使用的 LLM 提供者
   - 扩展版本

3. **联系支持：**
   - 在项目 GitHub 页面提交 Issue
   - 包含上述收集的信息

## ✅ 验证补全功能正常工作

当补全功能正常工作时，您应该看到：

1. **活动日志中的成功信息：**
   ```
   AICompletionSource: 参与补全 - 类型: Insertion, 字符: '.', 位置: 123
   AICompletionSource: 开始 AI 补全请求，上下文长度: 456
   AICompletionSource: 生成了 3 个补全建议
   ```

2. **Visual Studio 中的补全列表：**
   - 输入触发字符后出现补全下拉列表
   - 列表中包含 "AI 建议" 项目
   - 可以使用箭头键选择和 Enter 键接受

3. **响应时间合理：**
   - 本地 Ollama：1-3 秒
   - OpenAI API：0.5-2 秒

---

**记住：** 第一次使用时可能需要一些时间来初始化，请耐心等待。如果问题持续存在，请不要犹豫寻求帮助！
