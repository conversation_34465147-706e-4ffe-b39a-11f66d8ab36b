using System;
using System.Linq;
using Microsoft.VisualStudio.Shell;
using Microsoft.VisualStudio.Text;
using AICodeAssistant.Features.Completion;
using AICodeAssistant.Services;
using AICodeAssistant.UI;

namespace AICodeAssistant.Tests
{
    /// <summary>
    /// 测试补全响应解析功能
    /// </summary>
    public static class CompletionResponseTest
    {
        /// <summary>
        /// 测试 AI 响应解析
        /// </summary>
        public static void TestResponseParsing()
        {
            try
            {
                ActivityLog.LogInformation("CompletionResponseTest", "开始测试 AI 响应解析");

                // 模拟不同类型的 AI 响应
                var testResponses = new[]
                {
                    // 简单的单行响应
                    "string.Empty",
                    
                    // 多行响应
                    @"string.Empty
Console.WriteLine(""Hello"")
DateTime.Now",
                    
                    // 带代码块的响应
                    @"```csharp
string.Empty
Console.WriteLine(""Hello"")
DateTime.Now
```",
                    
                    // 带前缀的响应
                    @"这里是补全建议：
string.Empty
Console.WriteLine(""Hello"")
DateTime.Now",
                    
                    // 复杂的响应
                    @"以下是一些可能的代码补全：

```csharp
string.Empty
Console.WriteLine(""Hello World"")
DateTime.Now.ToString()
result.ToString()
```

这些建议基于当前的上下文。"
                };

                for (int i = 0; i < testResponses.Length; i++)
                {
                    var response = testResponses[i];
                    ActivityLog.LogInformation("CompletionResponseTest", $"测试响应 {i + 1}:");
                    ActivityLog.LogInformation("CompletionResponseTest", $"原始响应: {response}");

                    // 测试清理响应
                    var cleaned = TestCleanResponse(response);
                    ActivityLog.LogInformation("CompletionResponseTest", $"清理后: {cleaned}");

                    // 测试分割建议
                    var suggestions = TestSplitSuggestions(cleaned);
                    ActivityLog.LogInformation("CompletionResponseTest", $"建议数量: {suggestions.Length}");
                    
                    for (int j = 0; j < suggestions.Length; j++)
                    {
                        ActivityLog.LogInformation("CompletionResponseTest", $"  建议 {j + 1}: '{suggestions[j]}'");
                    }

                    ActivityLog.LogInformation("CompletionResponseTest", "---");
                }

                ActivityLog.LogInformation("CompletionResponseTest", "AI 响应解析测试完成");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("CompletionResponseTest", $"测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试清理响应（模拟 CleanAiResponse 方法）
        /// </summary>
        private static string TestCleanResponse(string response)
        {
            if (string.IsNullOrWhiteSpace(response))
                return string.Empty;

            // 移除代码块标记
            response = System.Text.RegularExpressions.Regex.Replace(response, @"```[\w]*\n?", "", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            response = System.Text.RegularExpressions.Regex.Replace(response, @"```", "", System.Text.RegularExpressions.RegexOptions.IgnoreCase);

            // 移除常见的 AI 回复前缀
            var prefixesToRemove = new[]
            {
                "这里是补全建议：",
                "建议的代码补全：",
                "代码补全：",
                "补全代码：",
                "以下是一些可能的代码补全：",
                "Here's the completion:",
                "Suggested completion:",
                "Code completion:"
            };

            foreach (var prefix in prefixesToRemove)
            {
                if (response.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
                {
                    response = response.Substring(prefix.Length).TrimStart();
                    break;
                }
            }

            return response.Trim();
        }

        /// <summary>
        /// 测试分割建议（模拟 ParseCompletionResponse 的分割逻辑）
        /// </summary>
        private static string[] TestSplitSuggestions(string cleanedResponse)
        {
            if (string.IsNullOrWhiteSpace(cleanedResponse))
                return new string[0];

            var suggestions = cleanedResponse.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(s => s.Trim())
                .Where(s => !string.IsNullOrEmpty(s) && !s.StartsWith("//") && !s.StartsWith("/*"))
                .Where(s => !s.StartsWith("这些建议") && !s.StartsWith("以上是") && !s.StartsWith("希望"))
                .Take(10)
                .ToArray();

            return suggestions;
        }

        /// <summary>
        /// 测试提示词构建
        /// </summary>
        public static void TestPromptBuilding()
        {
            try
            {
                ActivityLog.LogInformation("CompletionResponseTest", "开始测试提示词构建");

                // 创建模拟的代码上下文
                var context = new CodeContext
                {
                    FilePath = "TestFile.cs",
                    Language = "csharp",
                    BeforeCaret = @"using System;

public class TestClass
{
    public void TestMethod()
    {
        var result = ",
                    AfterCaret = @";
        Console.WriteLine(result);
    }
}",
                    CaretLine = 7,
                    CaretPosition = 85
                };

                ActivityLog.LogInformation("CompletionResponseTest", "模拟上下文创建完成");
                ActivityLog.LogInformation("CompletionResponseTest", $"文件: {context.FilePath}");
                ActivityLog.LogInformation("CompletionResponseTest", $"语言: {context.Language}");
                ActivityLog.LogInformation("CompletionResponseTest", $"光标前代码: {context.BeforeCaret}");
                ActivityLog.LogInformation("CompletionResponseTest", $"光标后代码: {context.AfterCaret}");

                // 测试提示词构建
                var prompt = TestBuildPrompt(context);
                ActivityLog.LogInformation("CompletionResponseTest", $"生成的提示词长度: {prompt.Length}");
                ActivityLog.LogInformation("CompletionResponseTest", $"提示词预览: {prompt.Substring(0, Math.Min(300, prompt.Length))}...");

                ActivityLog.LogInformation("CompletionResponseTest", "提示词构建测试完成");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("CompletionResponseTest", $"提示词构建测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试构建提示词（模拟 BuildCompletionPrompt 方法）
        /// </summary>
        private static string TestBuildPrompt(CodeContext context)
        {
            var language = GetLanguageFromFilePath(context.FilePath);

            var systemPrompt = $@"你是一名经验丰富的 {language} 程序员和代码助手。你的任务是根据给定的代码上下文，提供准确、简洁且符合最佳实践的代码补全建议。

请遵循以下原则：
1. 只提供代码补全，不要包含解释或注释
2. 确保代码语法正确且符合 {language} 的编码规范
3. 考虑上下文中的变量、方法和类型
4. 优先使用现有的变量和方法名
5. 保持代码风格与上下文一致

当前文件类型：{language}
文件路径：{context.FilePath}";

            var userPrompt = $@"【上下文开始】
{context.BeforeCaret}▮{context.AfterCaret}
【上下文结束】

请直接补全光标位置（▮）后续的代码，不要重复已有的代码：";

            return $"{systemPrompt}\n\n{userPrompt}";
        }

        /// <summary>
        /// 从文件路径获取编程语言
        /// </summary>
        private static string GetLanguageFromFilePath(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return "text";

            var extension = System.IO.Path.GetExtension(filePath)?.ToLowerInvariant();
            return extension switch
            {
                ".cs" => "csharp",
                ".vb" => "vb",
                ".cpp" or ".c" or ".h" or ".hpp" => "cpp",
                ".js" => "javascript",
                ".ts" => "typescript",
                ".py" => "python",
                ".java" => "java",
                ".xml" or ".xaml" => "xml",
                ".json" => "json",
                ".html" => "html",
                ".css" => "css",
                ".sql" => "sql",
                _ => "text"
            };
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            ActivityLog.LogInformation("CompletionResponseTest", "=== 开始补全响应测试 ===");
            
            TestResponseParsing();
            TestPromptBuilding();
            
            ActivityLog.LogInformation("CompletionResponseTest", "=== 补全响应测试完成 ===");
        }
    }
}
