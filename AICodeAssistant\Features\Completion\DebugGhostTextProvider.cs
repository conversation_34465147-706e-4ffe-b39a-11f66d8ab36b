using System;
using System.ComponentModel.Composition;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Media;
using Microsoft.VisualStudio.Text;
using Microsoft.VisualStudio.Text.Editor;
using Microsoft.VisualStudio.Text.Formatting;
using Microsoft.VisualStudio.Text.Tagging;
using Microsoft.VisualStudio.Utilities;
using Microsoft.VisualStudio.Shell;

namespace AICodeAssistant.Features.Completion
{
    /// <summary>
    /// 调试版本的Ghost Text提供者
    /// 简化实现，专注于解决触发问题
    /// </summary>
    [Export(typeof(AdornmentLayerDefinition))]
    [Name("DebugGhostTextLayer")]
    [Order(After = PredefinedAdornmentLayers.Selection, Before = PredefinedAdornmentLayers.Text)]
    internal static AdornmentLayerDefinition DebugGhostTextLayerDefinition;

    // Temporarily disabled for basic testing
    // [Export(typeof(IWpfTextViewCreationListener))]
    [ContentType("CSharp")]
    [ContentType("code")]
    [ContentType("text")]
    [ContentType("any")]
    [TextViewRole(PredefinedTextViewRoles.Editable)]
    [TextViewRole(PredefinedTextViewRoles.Document)]
    public class DebugGhostTextProvider : IWpfTextViewCreationListener
    {
        public void TextViewCreated(IWpfTextView textView)
        {
            try
            {
                ActivityLog.LogInformation("DebugGhostTextProvider", "=== DEBUG: Creating Ghost Text manager ===");
                
                var manager = new DebugGhostTextManager(textView);
                textView.Properties.AddProperty(typeof(DebugGhostTextManager), manager);
                
                ActivityLog.LogInformation("DebugGhostTextProvider", "=== DEBUG: Ghost Text manager created ===");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("DebugGhostTextProvider", $"=== DEBUG ERROR: {ex.Message} ===");
                ActivityLog.LogError("DebugGhostTextProvider", $"Stack: {ex.StackTrace}");
            }
        }
    }

    public class DebugGhostTextManager : IDisposable
    {
        private readonly IWpfTextView _textView;
        private readonly IAdornmentLayer _adornmentLayer;
        private Timer _triggerTimer;
        private string _currentGhostText;
        private SnapshotPoint? _ghostTextPosition;

        private const int TriggerDelayMs = 2000; // 2秒，更容易测试
        private const string AdornmentTag = "DebugGhostText";

        public DebugGhostTextManager(IWpfTextView textView)
        {
            _textView = textView ?? throw new ArgumentNullException(nameof(textView));
            
            try
            {
                ActivityLog.LogInformation("DebugGhostTextManager", "=== DEBUG: Initializing manager ===");
                
                // 获取装饰层
                _adornmentLayer = _textView.GetAdornmentLayer("DebugGhostTextLayer");
                ActivityLog.LogInformation("DebugGhostTextManager", $"=== DEBUG: Adornment layer: {_adornmentLayer != null} ===");

                // 订阅事件
                _textView.TextBuffer.Changed += OnTextChanged;
                _textView.KeyDown += OnKeyDown;
                _textView.Closed += OnClosed;

                ActivityLog.LogInformation("DebugGhostTextManager", "=== DEBUG: Manager initialized successfully ===");
                
                // 立即测试显示Ghost Text
                TestGhostTextDisplay();
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("DebugGhostTextManager", $"=== DEBUG INIT ERROR: {ex.Message} ===");
            }
        }

        private void TestGhostTextDisplay()
        {
            try
            {
                ActivityLog.LogInformation("DebugGhostTextManager", "=== DEBUG: Testing Ghost Text display ===");
                
                // 延迟3秒后显示测试Ghost Text
                Task.Delay(3000).ContinueWith(_ =>
                {
                    try
                    {
                        ThreadHelper.JoinableTaskFactory.Run(async () =>
                        {
                            await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                            
                            var caretPosition = _textView.Caret.Position.BufferPosition;
                            ShowTestGhostText("TEST_GHOST_TEXT", caretPosition);
                        });
                    }
                    catch (Exception ex)
                    {
                        ActivityLog.LogError("DebugGhostTextManager", $"=== DEBUG TEST ERROR: {ex.Message} ===");
                    }
                });
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("DebugGhostTextManager", $"=== DEBUG TEST SETUP ERROR: {ex.Message} ===");
            }
        }

        private void OnTextChanged(object sender, TextContentChangedEventArgs e)
        {
            try
            {
                ActivityLog.LogInformation("DebugGhostTextManager", "=== DEBUG: Text changed ===");
                
                ClearGhostText();

                if (e.Changes.Count > 0)
                {
                    var change = e.Changes[0];
                    ActivityLog.LogInformation("DebugGhostTextManager", $"=== DEBUG: Change - New: '{change.NewText}', Old: '{change.OldText}' ===");
                    
                    if (!string.IsNullOrEmpty(change.NewText) && change.OldText.Length == 0)
                    {
                        ActivityLog.LogInformation("DebugGhostTextManager", "=== DEBUG: Scheduling trigger ===");
                        ScheduleTrigger();
                    }
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("DebugGhostTextManager", $"=== DEBUG TEXT CHANGE ERROR: {ex.Message} ===");
            }
        }

        private void OnKeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(_currentGhostText))
                    return;

                ActivityLog.LogInformation("DebugGhostTextManager", $"=== DEBUG: Key pressed: {e.Key} ===");

                switch (e.Key)
                {
                    case Key.Tab:
                        ActivityLog.LogInformation("DebugGhostTextManager", "=== DEBUG: Tab - accepting Ghost Text ===");
                        AcceptGhostText();
                        e.Handled = true;
                        break;

                    case Key.Escape:
                        ActivityLog.LogInformation("DebugGhostTextManager", "=== DEBUG: Esc - clearing Ghost Text ===");
                        ClearGhostText();
                        e.Handled = true;
                        break;
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("DebugGhostTextManager", $"=== DEBUG KEY ERROR: {ex.Message} ===");
            }
        }

        private void OnClosed(object sender, EventArgs e)
        {
            ActivityLog.LogInformation("DebugGhostTextManager", "=== DEBUG: Text view closed ===");
            Dispose();
        }

        private void ScheduleTrigger()
        {
            try
            {
                _triggerTimer?.Dispose();

                _triggerTimer = new Timer(_ =>
                {
                    try
                    {
                        ActivityLog.LogInformation("DebugGhostTextManager", "=== DEBUG: Timer fired ===");
                        
                        ThreadHelper.JoinableTaskFactory.Run(async () =>
                        {
                            await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                            
                            var caretPosition = _textView.Caret.Position.BufferPosition;
                            var line = caretPosition.GetContainingLine();
                            var lineText = line.GetText();
                            
                            ActivityLog.LogInformation("DebugGhostTextManager", $"=== DEBUG: Line text: '{lineText}' ===");
                            
                            if (ShouldShowGhostText(caretPosition))
                            {
                                ShowTestGhostText("string.Empty", caretPosition);
                            }
                            else
                            {
                                ActivityLog.LogInformation("DebugGhostTextManager", "=== DEBUG: Conditions not met ===");
                            }
                        });
                    }
                    catch (Exception ex)
                    {
                        ActivityLog.LogError("DebugGhostTextManager", $"=== DEBUG TIMER ERROR: {ex.Message} ===");
                    }
                }, null, TriggerDelayMs, Timeout.Infinite);

                ActivityLog.LogInformation("DebugGhostTextManager", $"=== DEBUG: Timer scheduled for {TriggerDelayMs}ms ===");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("DebugGhostTextManager", $"=== DEBUG SCHEDULE ERROR: {ex.Message} ===");
            }
        }

        private bool ShouldShowGhostText(SnapshotPoint position)
        {
            try
            {
                var line = position.GetContainingLine();
                var lineText = line.GetText();
                var positionInLine = position.Position - line.Start.Position;

                ActivityLog.LogInformation("DebugGhostTextManager", $"=== DEBUG: Checking conditions ===");
                ActivityLog.LogInformation("DebugGhostTextManager", $"  Line: '{lineText}'");
                ActivityLog.LogInformation("DebugGhostTextManager", $"  Position: {positionInLine}/{lineText.Length}");

                // 简化条件：只要行长度大于3就显示
                if (lineText.Trim().Length >= 3)
                {
                    ActivityLog.LogInformation("DebugGhostTextManager", "=== DEBUG: Conditions met ===");
                    return true;
                }

                ActivityLog.LogInformation("DebugGhostTextManager", "=== DEBUG: Line too short ===");
                return false;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("DebugGhostTextManager", $"=== DEBUG CONDITION ERROR: {ex.Message} ===");
                return false;
            }
        }

        private void ShowTestGhostText(string text, SnapshotPoint position)
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                ActivityLog.LogInformation("DebugGhostTextManager", $"=== DEBUG: Showing Ghost Text: '{text}' ===");

                ClearGhostText();

                _currentGhostText = text;
                _ghostTextPosition = position;

                var textBlock = new System.Windows.Controls.TextBlock
                {
                    Text = text,
                    Foreground = new SolidColorBrush(Colors.Red), // 使用红色更明显
                    Opacity = 0.8,
                    FontFamily = _textView.FormattedLineSource.DefaultTextProperties.Typeface.FontFamily,
                    FontSize = _textView.FormattedLineSource.DefaultTextProperties.FontRenderingEmSize,
                    FontWeight = System.Windows.FontWeights.Bold,
                    ToolTip = "DEBUG Ghost Text - Press Tab to accept, Esc to dismiss"
                };

                var span = new SnapshotSpan(position, 0);

                _adornmentLayer.AddAdornment(
                    AdornmentPositioningBehavior.TextRelative,
                    span,
                    AdornmentTag,
                    textBlock,
                    null);

                ActivityLog.LogInformation("DebugGhostTextManager", "=== DEBUG: Ghost Text adornment added ===");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("DebugGhostTextManager", $"=== DEBUG SHOW ERROR: {ex.Message} ===");
                ActivityLog.LogError("DebugGhostTextManager", $"Stack: {ex.StackTrace}");
            }
        }

        private void AcceptGhostText()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                if (string.IsNullOrEmpty(_currentGhostText) || !_ghostTextPosition.HasValue)
                    return;

                ActivityLog.LogInformation("DebugGhostTextManager", $"=== DEBUG: Accepting: '{_currentGhostText}' ===");

                var edit = _textView.TextBuffer.CreateEdit();
                edit.Insert(_ghostTextPosition.Value.Position, _currentGhostText);
                edit.Apply();

                ClearGhostText();
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("DebugGhostTextManager", $"=== DEBUG ACCEPT ERROR: {ex.Message} ===");
            }
        }

        private void ClearGhostText()
        {
            try
            {
                if (!string.IsNullOrEmpty(_currentGhostText))
                {
                    ThreadHelper.ThrowIfNotOnUIThread();
                    
                    ActivityLog.LogInformation("DebugGhostTextManager", "=== DEBUG: Clearing Ghost Text ===");
                    
                    _adornmentLayer.RemoveAllAdornments();
                    _currentGhostText = null;
                    _ghostTextPosition = null;
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("DebugGhostTextManager", $"=== DEBUG CLEAR ERROR: {ex.Message} ===");
            }
        }

        public void Dispose()
        {
            try
            {
                ActivityLog.LogInformation("DebugGhostTextManager", "=== DEBUG: Disposing ===");

                _triggerTimer?.Dispose();
                ClearGhostText();

                if (_textView != null)
                {
                    _textView.TextBuffer.Changed -= OnTextChanged;
                    _textView.KeyDown -= OnKeyDown;
                    _textView.Closed -= OnClosed;
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("DebugGhostTextManager", $"=== DEBUG DISPOSE ERROR: {ex.Message} ===");
            }
        }
    }
}
