<UserControl x:Class="AICodeAssistant.ChatToolWindowControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:toolkit="clr-namespace:Community.VisualStudio.Toolkit;assembly=Community.VisualStudio.Toolkit"
             toolkit:Themes.UseVsTheme="True"
             mc:Ignorable="d"
             d:DesignHeight="600"
             d:DesignWidth="400"
             Name="ChatToolWindow">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <ToolBar Grid.Row="0" ToolBarTray.IsLocked="True">
            <Button Name="ClearChatButton" Click="ClearChat_Click" ToolTip="清空聊天记录" Content="清空" Padding="5,2"/>
            <Button Name="ExportChatButton" Click="ExportChat_Click" ToolTip="导出聊天记录" Content="导出" Padding="5,2"/>
            <Separator/>
            <ComboBox Name="ProviderComboBox" Width="100" SelectionChanged="ProviderComboBox_SelectionChanged" ToolTip="选择 AI 提供者">
                <ComboBoxItem Content="Ollama" Tag="Ollama"/>
                <ComboBoxItem Content="OpenAI" Tag="OpenAI"/>
            </ComboBox>
        </ToolBar>

        <!-- 聊天消息显示区域 -->
        <ScrollViewer Grid.Row="1" Name="ChatScrollViewer" VerticalScrollBarVisibility="Auto" Margin="5">
            <StackPanel Name="ChatMessagesPanel" Orientation="Vertical"/>
        </ScrollViewer>

        <!-- 当前文件信息 -->
        <Border Grid.Row="2" Background="{DynamicResource {x:Static SystemColors.ControlBrushKey}}"
                BorderBrush="{DynamicResource {x:Static SystemColors.ControlDarkBrushKey}}"
                BorderThickness="0,1,0,0" Padding="5">
            <TextBlock Name="CurrentFileLabel" Text="当前文件: 无" FontSize="10" Opacity="0.7"/>
        </Border>

        <!-- Token 统计信息 -->
        <Border Grid.Row="3" Background="LightGray" Padding="5" Margin="5,2,5,0">
            <TextBlock Name="TokenStatsDisplay" Text="Token 统计: 历史 0 / 上下文 0 / 总计 0" FontSize="10" Opacity="0.8"/>
        </Border>

        <!-- 上下文截断提示 -->
        <Border Name="ContextWarningPanel" Grid.Row="4"
                Background="Orange" Padding="8" Margin="5,2,5,0" Visibility="Collapsed">
            <TextBlock Name="ContextWarningLabel"
                       Text="⚠️ 对话超出最大上下文 Token 数，上下文消息将被截断，建议新建对话"
                       FontSize="11" Foreground="White" TextWrapping="Wrap"/>
        </Border>

        <!-- 输入区域 -->
        <Grid Grid.Row="5" Margin="5">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 快捷命令提示 -->
            <TextBlock Grid.Row="0" Grid.ColumnSpan="2" Text="快捷命令: /explain, /refactor, /test, /doc, /search, /clear"
                       FontSize="9" Opacity="0.6" Margin="0,0,0,2"/>

            <!-- 输入区域容器 -->
            <Border Grid.Row="1" Grid.Column="0" Background="Black" Padding="10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 输入框 -->
                    <TextBox Name="InputTextBox" Grid.Row="0"
                             AcceptsReturn="False" TextWrapping="Wrap"
                             MaxHeight="100" VerticalScrollBarVisibility="Auto"
                             KeyDown="InputTextBox_KeyDown"
                             PreviewKeyDown="InputTextBox_PreviewKeyDown"
                             Text="输入您的问题或选择代码后提问..."
                             Foreground="White" Background="Black"
                             BorderThickness="1" BorderBrush="Gray"
                             Padding="8"
                             GotFocus="InputTextBox_GotFocus" LostFocus="InputTextBox_LostFocus">
                        <TextBox.ContextMenu>
                            <ContextMenu>
                                <MenuItem Header="复制" Command="ApplicationCommands.Copy"/>
                                <MenuItem Header="粘贴" Command="ApplicationCommands.Paste"/>
                                <MenuItem Header="剪切" Command="ApplicationCommands.Cut"/>
                                <Separator/>
                                <MenuItem Header="全选" Command="ApplicationCommands.SelectAll"/>
                            </ContextMenu>
                        </TextBox.ContextMenu>
                    </TextBox>

                    <!-- 编辑模式下的发送按钮 - 移动到消息内容区域的右下角 -->
                    <Button Name="EditSendButton" Grid.Row="1"
                            Content="发送" Width="60" Height="25"
                            HorizontalAlignment="Right" Margin="0,5,0,0"
                            Visibility="Collapsed" Click="EditSendButton_Click"
                            Background="#0078D4" Foreground="White" BorderThickness="0"/>
                </Grid>
            </Border>

            <!-- 发送按钮 -->
            <Button Name="SendButton" Grid.Row="1" Grid.Column="1"
                    Content="发送" Width="60" Margin="5,0,0,0"
                    Click="SendButton_Click" IsDefault="True"/>
        </Grid>
    </Grid>
</UserControl>
