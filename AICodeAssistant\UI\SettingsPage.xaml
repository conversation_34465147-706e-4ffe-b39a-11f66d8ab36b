<UserControl x:Class="AICodeAssistant.UI.SettingsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:toolkit="clr-namespace:Community.VisualStudio.Toolkit;assembly=Community.VisualStudio.Toolkit"
             toolkit:Themes.UseVsTheme="True"
             mc:Ignorable="d"
             d:DesignHeight="600"
             d:DesignWidth="500">
    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="20" Orientation="Vertical">
            <TextBlock Text="AI Code Assistant 设置" FontSize="18" FontWeight="Bold" Margin="0,0,0,20"/>

            <!-- LLM 提供者设置 -->
            <GroupBox Header="LLM 提供者设置" Margin="0,0,0,20">
                <StackPanel Margin="10">
                    <TextBlock Text="选择 AI 提供者:" Margin="0,0,0,5"/>
                    <ComboBox Name="ProviderComboBox" Width="200" HorizontalAlignment="Left" Margin="0,0,0,15">
                        <ComboBoxItem Content="Ollama" Tag="Ollama"/>
                        <ComboBoxItem Content="OpenAI" Tag="OpenAI"/>
                    </ComboBox>

                    <!-- OpenAI 设置 -->
                    <StackPanel Name="OpenAISettings" Visibility="Collapsed">
                        <TextBlock Text="OpenAI API Key:" Margin="0,0,0,5"/>
                        <PasswordBox Name="OpenAIApiKeyBox" Width="300" HorizontalAlignment="Left" Margin="0,0,0,10"/>
                        
                        <TextBlock Text="API Base URL (可选):" Margin="0,0,0,5"/>
                        <TextBox Name="OpenAIApiBaseBox" Width="300" HorizontalAlignment="Left" 
                                 Text="https://api.openai.com" Margin="0,0,0,10"/>
                        
                        <TextBlock Text="模型:" Margin="0,0,0,5"/>
                        <ComboBox Name="OpenAIModelBox" Width="200" HorizontalAlignment="Left" Margin="0,0,0,15">
                            <ComboBoxItem Content="gpt-3.5-turbo" IsSelected="True"/>
                            <ComboBoxItem Content="gpt-4"/>
                            <ComboBoxItem Content="gpt-4-turbo"/>
                        </ComboBox>
                    </StackPanel>

                    <!-- Ollama 设置 -->
                    <StackPanel Name="OllamaSettings" Visibility="Visible">
                        <TextBlock Text="Ollama API Base URL:" Margin="0,0,0,5"/>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <TextBox Name="OllamaApiBaseBox" Width="250"
                                     Text="http://localhost:11434" Margin="0,0,10,0"/>
                            <Button Name="TestConnectionButton" Content="测试连接" Width="80"
                                    Margin="0,0,0,0" Click="TestConnection_Click"/>
                        </StackPanel>

                        <TextBlock Text="模型:" Margin="0,0,0,5"/>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                            <ComboBox Name="OllamaModelBox" Width="200" HorizontalAlignment="Left"
                                      Margin="0,0,10,0" IsEditable="True"
                                      ItemsSource="{Binding AvailableModels}"
                                      SelectedValue="{Binding SelectedModel, Mode=TwoWay}">
                            </ComboBox>
                            <Button Name="RefreshModelsButton" Content="刷新模型" Width="80"
                                    Click="RefreshModels_Click" IsEnabled="True"/>
                        </StackPanel>
                        <TextBlock Name="ModelStatusLabel" Text="" FontSize="10" Opacity="0.7"
                                   Margin="0,0,0,15" Foreground="Gray"/>
                    </StackPanel>
                </StackPanel>
            </GroupBox>

            <!-- 补全设置 -->
            <GroupBox Header="代码补全设置" Margin="0,0,0,20">
                <StackPanel Margin="10">
                    <CheckBox Name="EnableCompletionCheckBox" Content="启用 AI 代码补全" IsChecked="True" Margin="0,0,0,10"/>
                    
                    <TextBlock Text="上下文行数:" Margin="0,0,0,5"/>
                    <Slider Name="ContextLinesSlider" Minimum="5" Maximum="50" Value="20" 
                            Width="200" HorizontalAlignment="Left" Margin="0,0,0,5"/>
                    <TextBlock Name="ContextLinesLabel" Text="20 行" FontSize="10" Opacity="0.7" Margin="0,0,0,10"/>
                    
                    <TextBlock Text="最大生成 Token 数:" Margin="0,0,0,5"/>
                    <Slider Name="MaxTokensSlider" Minimum="256" Maximum="4096" Value="2048" 
                            Width="200" HorizontalAlignment="Left" Margin="0,0,0,5"/>
                    <TextBlock Name="MaxTokensLabel" Text="2048 tokens" FontSize="10" Opacity="0.7" Margin="0,0,0,10"/>
                    
                    <TextBlock Text="温度 (创造性):" Margin="0,0,0,5"/>
                    <Slider Name="TemperatureSlider" Minimum="0" Maximum="1" Value="0.7" 
                            Width="200" HorizontalAlignment="Left" Margin="0,0,0,5"/>
                    <TextBlock Name="TemperatureLabel" Text="0.7" FontSize="10" Opacity="0.7" Margin="0,0,0,10"/>
                </StackPanel>
            </GroupBox>

            <!-- 聊天设置 -->
            <GroupBox Header="聊天设置" Margin="0,0,0,20">
                <StackPanel Margin="10">
                    <TextBlock Text="最大上下文 Token 数:" Margin="0,0,0,5"/>
                    <Slider Name="MaxContextLengthSlider" Minimum="1024" Maximum="262144" Value="8192"
                            Width="200" HorizontalAlignment="Left" Margin="0,0,0,5"/>
                    <TextBlock Name="MaxContextLengthLabel" Text="8192 tokens (8K)" FontSize="10" Opacity="0.7" Margin="0,0,0,10"/>

                    <TextBlock Text="最大输出 Token 数:" Margin="0,0,0,5"/>
                    <Slider Name="MaxOutputTokensControl" Minimum="256" Maximum="8192" Value="2048"
                            Width="200" HorizontalAlignment="Left" Margin="0,0,0,5"/>
                    <TextBlock Name="MaxOutputTokensDisplay" Text="2048 tokens" FontSize="10" Opacity="0.7" Margin="0,0,0,10"/>

                    <CheckBox Name="AutoSaveChatCheckBox" Content="自动保存聊天记录" IsChecked="True" Margin="0,0,0,10"/>
                </StackPanel>
            </GroupBox>

            <!-- 安全设置 -->
            <GroupBox Header="安全设置" Margin="0,0,0,20">
                <StackPanel Margin="10">
                    <CheckBox Name="MaskSensitiveDataCheckBox" Content="自动过滤敏感数据" IsChecked="True" Margin="0,0,0,10"/>
                    <TextBlock Text="启用此选项将自动检测并过滤 API 密钥、密码等敏感信息" 
                               FontSize="10" Opacity="0.7" TextWrapping="Wrap" Margin="0,0,0,10"/>
                </StackPanel>
            </GroupBox>

            <!-- 重置确认 -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,20,0,10">
                <CheckBox Name="ConfirmResetCheckBox" Content="确认重置所有设置到默认值"
                          VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock Text="(勾选后点击重置按钮)" FontSize="10" Opacity="0.7"
                           VerticalAlignment="Center" Foreground="Gray"/>
            </StackPanel>

            <!-- 状态显示区域 -->
            <Border Name="StatusBorder" Background="LightBlue" BorderBrush="Blue" BorderThickness="1"
                    Margin="0,10,0,10" Padding="10" Visibility="Collapsed" CornerRadius="3">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Name="StatusIcon" Text="ℹ" FontSize="16" Margin="0,0,10,0"
                               VerticalAlignment="Center" Foreground="Blue"/>
                    <TextBlock Name="StatusMessage" Text="" VerticalAlignment="Center"
                               TextWrapping="Wrap" Foreground="DarkBlue"/>
                </StackPanel>
            </Border>

            <!-- 按钮 -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10,0,0">
                <Button Name="ResetButton" Content="重置" Width="60" Margin="0,0,10,0" Click="Reset_Click"/>
                <Button Name="SaveButton" Content="保存" Width="60" Click="Save_Click"/>
            </StackPanel>
        </StackPanel>
    </ScrollViewer>
</UserControl>
