# Ghost Text 故障排除指南

## 🚨 问题诊断

我发现Ghost Text未正确触发的问题，已创建了一个调试版本来帮助诊断问题。

## 🔧 修复措施

### 1. 创建了调试版本
**新文件**: `DebugGhostTextProvider.cs`
- 简化的实现，专注于基本功能
- 详细的调试日志（所有日志都有 `=== DEBUG:` 前缀）
- 更明显的视觉效果（红色粗体文本）
- 自动测试功能

### 2. 修复了装饰层问题
```csharp
// 添加了装饰层定义
[Export(typeof(AdornmentLayerDefinition))]
[Name("DebugGhostTextLayer")]
[Order(After = PredefinedAdornmentLayers.Selection, Before = PredefinedAdornmentLayers.Text)]
internal static AdornmentLayerDefinition DebugGhostTextLayerDefinition;
```

### 3. 扩展了MEF属性
```csharp
[Export(typeof(IWpfTextViewCreationListener))]
[ContentType("CSharp")]
[ContentType("code")]
[ContentType("text")]
[ContentType("any")]
[TextViewRole(PredefinedTextViewRoles.Editable)]
[TextViewRole(PredefinedTextViewRoles.Document)]
```

## 🧪 测试步骤

### 步骤1: 编译和安装
```bash
1. Clean Solution
2. Rebuild Solution
3. 卸载旧扩展
4. 重启 Visual Studio
5. 安装新的 .vsix 文件
```

### 步骤2: 验证MEF组件加载
打开Activity Log，查找：
```
=== DEBUG: Creating Ghost Text manager ===
=== DEBUG: Ghost Text manager created ===
=== DEBUG: Initializing manager ===
=== DEBUG: Adornment layer: True ===
=== DEBUG: Manager initialized successfully ===
```

### 步骤3: 自动测试
调试版本会在3秒后自动显示测试Ghost Text：
- 打开任何C#文件
- 等待3秒
- 应该看到红色粗体的 "TEST_GHOST_TEXT" 出现

### 步骤4: 手动测试
```csharp
// 在C#文件中输入：
var result = 
// 停止输入，等待2秒
// 应该看到红色的 "string.Empty" 出现
```

### 步骤5: 交互测试
- 看到Ghost Text后按Tab键 → 应该插入文本
- 看到Ghost Text后按Esc键 → 应该清除文本

## 🔍 调试日志关键词

在Activity Log中搜索这些关键词：

### MEF组件加载
```
=== DEBUG: Creating Ghost Text manager ===
=== DEBUG: Ghost Text manager created ===
```

### 初始化
```
=== DEBUG: Initializing manager ===
=== DEBUG: Adornment layer: True ===
```

### 文本变化
```
=== DEBUG: Text changed ===
=== DEBUG: Change - New: 'x', Old: '' ===
=== DEBUG: Scheduling trigger ===
```

### 定时器触发
```
=== DEBUG: Timer fired ===
=== DEBUG: Line text: 'var result = ' ===
=== DEBUG: Checking conditions ===
=== DEBUG: Conditions met ===
```

### Ghost Text显示
```
=== DEBUG: Showing Ghost Text: 'string.Empty' ===
=== DEBUG: Ghost Text adornment added ===
```

### 用户交互
```
=== DEBUG: Key pressed: Tab ===
=== DEBUG: Tab - accepting Ghost Text ===
=== DEBUG: Accepting: 'string.Empty' ===
```

## 🚨 常见问题诊断

### 问题1: 没有看到任何DEBUG日志
**可能原因**: MEF组件未加载
**解决方案**:
- 确认扩展正确安装
- 检查项目编译是否成功
- 重启Visual Studio
- 检查.vsix文件是否包含新文件

### 问题2: 看到初始化日志但没有自动测试
**可能原因**: 装饰层问题
**检查日志**:
```
=== DEBUG: Adornment layer: False ===
```
**解决方案**:
- 检查装饰层定义是否正确导出
- 确认using语句包含所需命名空间

### 问题3: 定时器触发但没有显示Ghost Text
**可能原因**: UI线程问题或装饰层添加失败
**检查日志**:
```
=== DEBUG SHOW ERROR: xxx ===
```
**解决方案**:
- 检查UI线程切换是否正确
- 验证装饰层API调用

### 问题4: Ghost Text显示但不可见
**可能原因**: 视觉样式问题
**解决方案**:
- 调试版本使用红色粗体，应该很明显
- 检查编辑器主题是否影响显示
- 尝试调整颜色和透明度

## 🔧 进一步调试

### 如果调试版本工作正常
1. 逐步启用原版本功能
2. 添加AI集成
3. 恢复正常的视觉样式

### 如果调试版本仍有问题
1. 检查Visual Studio版本兼容性
2. 验证MEF导出属性
3. 检查装饰层API使用
4. 确认事件订阅是否正确

## 📋 测试清单

完成以下测试以确认功能正常：

- [ ] MEF组件成功加载
- [ ] 装饰层可用
- [ ] 自动测试显示红色文本
- [ ] 文本输入触发定时器
- [ ] 条件检查通过
- [ ] Ghost Text成功显示
- [ ] Tab键接受功能
- [ ] Esc键清除功能
- [ ] 文本正确插入编辑器

## 🎯 预期结果

如果一切正常，您应该看到：

1. **Activity Log中的完整调试流程**
2. **3秒后自动显示的红色测试文本**
3. **输入代码后2秒显示的Ghost Text**
4. **Tab/Esc键正常工作**

## 📞 下一步

根据调试结果：

### 如果调试版本工作
- 修复原版本的问题
- 集成AI功能
- 恢复正常样式

### 如果仍有问题
- 提供详细的Activity Log
- 检查Visual Studio版本
- 验证扩展安装

---

**请运行调试版本并提供Activity Log中的DEBUG日志，这将帮助我们快速定位问题！**
