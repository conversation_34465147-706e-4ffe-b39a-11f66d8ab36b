using System;

namespace AICodeAssistant.Tests
{
    /// <summary>
    /// 简单的思考内容过滤测试类
    /// 用于验证 FilterThinkingContent 方法的正确性
    /// </summary>
    public static class ThinkingContentFilterTest
    {
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("开始运行思考内容过滤测试...");
            
            TestBasicFiltering();
            TestMultipleThinkTags();
            TestEmptyContent();
            TestContentWithoutThinkTags();
            TestNestedContent();
            
            Console.WriteLine("所有测试完成！");
        }

        /// <summary>
        /// 测试基本过滤功能
        /// </summary>
        private static void TestBasicFiltering()
        {
            var content = @"这是正文内容。

<think>
这是思考过程，应该被移除。
包含多行内容。
</think>

这是更多正文内容。";

            var expected = @"这是正文内容。

这是更多正文内容。";

            var result = FilterThinkingContentHelper(content);
            
            if (result.Trim() == expected.Trim())
            {
                Console.WriteLine("✓ 基本过滤测试通过");
            }
            else
            {
                Console.WriteLine("✗ 基本过滤测试失败");
                Console.WriteLine($"期望: {expected.Trim()}");
                Console.WriteLine($"实际: {result.Trim()}");
            }
        }

        /// <summary>
        /// 测试多个思考标签
        /// </summary>
        private static void TestMultipleThinkTags()
        {
            var content = @"开始内容。

<think>第一个思考</think>

中间内容。

<think>
第二个思考
多行内容
</think>

结束内容。";

            var expected = @"开始内容。

中间内容。

结束内容。";

            var result = FilterThinkingContentHelper(content);
            
            if (result.Trim() == expected.Trim())
            {
                Console.WriteLine("✓ 多个思考标签测试通过");
            }
            else
            {
                Console.WriteLine("✗ 多个思考标签测试失败");
                Console.WriteLine($"期望: {expected.Trim()}");
                Console.WriteLine($"实际: {result.Trim()}");
            }
        }

        /// <summary>
        /// 测试空内容
        /// </summary>
        private static void TestEmptyContent()
        {
            var result1 = FilterThinkingContentHelper("");
            var result2 = FilterThinkingContentHelper(null);
            
            if (result1 == "" && result2 == null)
            {
                Console.WriteLine("✓ 空内容测试通过");
            }
            else
            {
                Console.WriteLine("✗ 空内容测试失败");
            }
        }

        /// <summary>
        /// 测试没有思考标签的内容
        /// </summary>
        private static void TestContentWithoutThinkTags()
        {
            var content = "这是没有思考标签的正常内容。";
            var result = FilterThinkingContentHelper(content);
            
            if (result == content)
            {
                Console.WriteLine("✓ 无思考标签测试通过");
            }
            else
            {
                Console.WriteLine("✗ 无思考标签测试失败");
            }
        }

        /// <summary>
        /// 测试嵌套内容
        /// </summary>
        private static void TestNestedContent()
        {
            var content = @"正文开始

<think>
思考过程中包含一些特殊字符：
- 列表项
- 另一个列表项

代码示例：
```csharp
var test = ""hello"";
```
</think>

正文结束";

            var expected = @"正文开始

正文结束";

            var result = FilterThinkingContentHelper(content);
            
            if (result.Trim() == expected.Trim())
            {
                Console.WriteLine("✓ 嵌套内容测试通过");
            }
            else
            {
                Console.WriteLine("✗ 嵌套内容测试失败");
                Console.WriteLine($"期望: {expected.Trim()}");
                Console.WriteLine($"实际: {result.Trim()}");
            }
        }

        /// <summary>
        /// 辅助方法，模拟 FilterThinkingContent 的逻辑
        /// </summary>
        private static string FilterThinkingContentHelper(string content)
        {
            if (string.IsNullOrEmpty(content))
                return content;

            try
            {
                // 使用正则表达式移除 <think>...</think> 标签及其内容
                var thinkPattern = @"<think\s*>.*?</think\s*>";
                var filteredContent = System.Text.RegularExpressions.Regex.Replace(
                    content,
                    thinkPattern,
                    "",
                    System.Text.RegularExpressions.RegexOptions.Singleline |
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                // 清理多余的空行和空白字符
                filteredContent = System.Text.RegularExpressions.Regex.Replace(
                    filteredContent,
                    @"\n\s*\n\s*\n",
                    "\n\n");

                return filteredContent.Trim();
            }
            catch (Exception)
            {
                return content; // 如果过滤失败，返回原始内容
            }
        }
    }
}
