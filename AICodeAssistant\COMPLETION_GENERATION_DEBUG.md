# AI 代码补全生成调试指南

## 🔍 问题诊断：补全功能触发但未生成代码

如果补全功能已经被正确触发（在活动日志中看到 "InitializeCompletion 被调用"），但是没有生成补全代码，请按照以下步骤进行调试：

## 📋 调试检查清单

### 1. 检查活动日志中的关键信息

在 Visual Studio 活动日志中查找以下关键消息：

**✅ 正常流程应该看到：**
```
AICompletionSource: InitializeCompletion 被调用
AICompletionSource: 参与补全
AICompletionSource: 上下文提取成功
AICompletionSource: 开始 AI 补全请求，提示词长度: XXX
AICompletionSource: AI 响应长度: XXX
AICompletionSource: 开始解析 AI 响应
AICompletionSource: 生成了 X 个补全建议
```

**❌ 问题指示器：**
```
AICompletionSource: 上下文提取失败
AICompletionSource: AI 响应为空
AICompletionSource: 清理后的响应为空
AICompletionSource: 过滤后建议数量: 0
AICompletionSource: 创建补全项失败
```

### 2. 常见问题和解决方案

#### 问题 1：上下文提取失败
**症状：** 日志显示 "上下文提取失败"
**原因：** ContextExtractor 无法正确提取代码上下文
**解决方案：**
1. 确保在支持的文件类型中测试（.cs, .js, .py 等）
2. 确保文件已保存
3. 尝试在不同的代码位置触发补全

#### 问题 2：AI 响应为空
**症状：** 日志显示 "AI 响应为空" 或 "AI 响应长度: 0"
**原因：** LLM 提供者没有返回有效响应

**Ollama 解决方案：**
```bash
# 检查 Ollama 服务状态
curl http://localhost:11434/api/tags

# 测试模型响应
curl -X POST http://localhost:11434/api/generate -d '{
  "model": "codellama",
  "prompt": "Complete this C# code: var result = ",
  "stream": false
}'
```

**OpenAI 解决方案：**
1. 验证 API 密钥正确
2. 检查账户余额
3. 验证网络连接

#### 问题 3：响应解析失败
**症状：** 日志显示 "清理后的响应为空" 或 "过滤后建议数量: 0"
**原因：** AI 响应格式不符合预期

**调试步骤：**
1. 查看活动日志中的 "AI 响应预览"
2. 检查响应是否包含有效的代码建议
3. 验证响应清理逻辑是否正确

#### 问题 4：补全项创建失败
**症状：** 日志显示 "创建补全项失败"
**原因：** CompletionItem 创建过程中出现异常

**解决方案：**
1. 检查 applicableToSpan 是否有效
2. 验证显示文本提取逻辑
3. 确认没有空的建议文本

### 3. 手动测试步骤

#### 测试 1：基本触发测试
```csharp
// 在 .cs 文件中输入以下代码
public class Test
{
    public void Method()
    {
        var result = // 在这里按 Ctrl+Space 或输入 .
```

#### 测试 2：不同触发方式
- **字符触发：** 输入 `.` `(` `[` 等字符
- **手动触发：** 按 `Ctrl+Space`
- **连续输入：** 输入多个字母后等待

#### 测试 3：不同代码上下文
```csharp
// 测试方法调用
string text = "hello";
text. // 触发补全

// 测试变量声明
var result = // 触发补全

// 测试类型实例化
var date = new DateTime // 触发补全
```

### 4. 提示词优化

如果 AI 响应质量不佳，可以尝试以下优化：

#### 优化后的提示词模板
```
你是一个专业的 C# 代码补全助手。请根据给定的代码上下文，提供简洁准确的代码补全建议。

重要规则：
1. 只返回代码补全内容，不要任何解释文字
2. 每行一个建议，最多提供5个建议
3. 确保语法正确且符合 C# 规范
4. 优先使用上下文中已有的变量和方法
5. 不要重复光标前已有的代码

代码上下文：
var result = ▮;

请补全光标位置（▮）的代码，每行一个建议：
```

#### 预期的 AI 响应格式
```
string.Empty
DateTime.Now
Console.ReadLine()
new StringBuilder()
GetDefaultValue()
```

### 5. 性能调优

#### 优化设置
```
上下文行数: 10-15 行（减少噪音）
最大 Token 数: 512-1024（快速响应）
温度参数: 0.3-0.5（更确定的结果）
```

#### 模型选择
- **Ollama：** 推荐使用 `codellama` 或 `deepseek-coder`
- **OpenAI：** 推荐使用 `gpt-3.5-turbo` 或 `gpt-4`

### 6. 高级调试

#### 启用详细日志
在代码中临时添加更多日志：
```csharp
ActivityLog.LogInformation("DEBUG", $"原始 AI 响应: {response}");
ActivityLog.LogInformation("DEBUG", $"清理后响应: {cleanedResponse}");
ActivityLog.LogInformation("DEBUG", $"分割后行数: {suggestions.Count}");
```

#### 模拟测试
使用 `CompletionResponseTest.RunAllTests()` 来测试响应解析逻辑：
```csharp
// 在诊断命令中运行
CompletionResponseTest.RunAllTests();
```

### 7. 常见 AI 响应问题

#### 问题：AI 返回解释文字而不是代码
**示例响应：**
```
这里有一些可能的补全建议：
1. string.Empty - 表示空字符串
2. DateTime.Now - 获取当前时间
```

**解决方案：** 优化提示词，强调只返回代码

#### 问题：AI 返回完整代码块而不是简单补全
**示例响应：**
```csharp
var result = string.Empty;
Console.WriteLine(result);
```

**解决方案：** 在提示词中强调只补全光标位置

#### 问题：AI 响应格式不一致
**解决方案：** 在响应清理逻辑中添加更多格式处理

### 8. 验证修复

修复后，应该在活动日志中看到：
```
AICompletionSource: 上下文提取成功 - 文件: TestFile.cs, 语言: csharp
AICompletionSource: 开始 AI 补全请求，提示词长度: 456
AICompletionSource: AI 响应长度: 123
AICompletionSource: AI 响应预览: string.Empty\nDateTime.Now\nConsole.ReadLine()...
AICompletionSource: 开始解析 AI 响应
AICompletionSource: 清理后响应长度: 89
AICompletionSource: 分割后行数: 3
AICompletionSource: 过滤后建议数量: 3
AICompletionSource: 处理建议 1: 'string.Empty'
AICompletionSource: 成功创建补全项: string.Empty
AICompletionSource: 最终创建了 3 个补全项
```

## 🎯 快速修复检查

1. **重启测试：** 重启 Visual Studio 和 Ollama 服务
2. **简化测试：** 使用最简单的代码上下文测试
3. **网络检查：** 确保 LLM 服务可访问
4. **设置验证：** 确认补全功能已启用
5. **模型测试：** 尝试不同的 AI 模型

如果问题仍然存在，请收集活动日志信息并查看具体的错误消息进行进一步诊断。
