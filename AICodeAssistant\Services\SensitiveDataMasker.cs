using System;
using System.Text.RegularExpressions;

namespace AICodeAssistant.Services
{
    /// <summary>
    /// 敏感数据过滤器，用于在发送到 LLM 前过滤敏感信息
    /// </summary>
    public static class SensitiveDataMasker
    {
        // 常见的敏感数据模式
        private static readonly Regex[] SensitivePatterns = new[]
        {
            // API Keys
            new Regex(@"(?i)(api[_-]?key|apikey)\s*[:=]\s*['""]?([a-zA-Z0-9_\-]{20,})['""]?", RegexOptions.Compiled),
            
            // Access Tokens
            new Regex(@"(?i)(access[_-]?token|accesstoken)\s*[:=]\s*['""]?([a-zA-Z0-9_\-\.]{20,})['""]?", RegexOptions.Compiled),
            
            // Connection Strings
            new Regex(@"(?i)(connection[_-]?string|connectionstring)\s*[:=]\s*['""]?([^'"";\r\n]{20,})['""]?", RegexOptions.Compiled),
            
            // Passwords
            new Regex(@"(?i)(password|pwd)\s*[:=]\s*['""]?([^'"";\s]{6,})['""]?", RegexOptions.Compiled),
            
            // JWT Tokens
            new Regex(@"eyJ[a-zA-Z0-9_\-]+\.eyJ[a-zA-Z0-9_\-]+\.[a-zA-Z0-9_\-]+", RegexOptions.Compiled),
            
            // Email addresses (部分遮蔽)
            new Regex(@"\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b", RegexOptions.Compiled),
            
            // IP Addresses (私有IP除外)
            new Regex(@"\b(?!(?:10|127|169\.254|172\.(?:1[6-9]|2\d|3[01])|192\.168)\.)(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b", RegexOptions.Compiled),
            
            // Credit Card Numbers
            new Regex(@"\b(?:\d{4}[-\s]?){3}\d{4}\b", RegexOptions.Compiled),
            
            // Social Security Numbers
            new Regex(@"\b\d{3}-\d{2}-\d{4}\b", RegexOptions.Compiled),
            
            // Phone Numbers
            new Regex(@"\b(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b", RegexOptions.Compiled)
        };

        /// <summary>
        /// 过滤文本中的敏感数据
        /// </summary>
        /// <param name="text">原始文本</param>
        /// <returns>过滤后的文本</returns>
        public static string MaskSensitiveData(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            var maskedText = text;

            try
            {
                // 应用所有敏感数据模式
                foreach (var pattern in SensitivePatterns)
                {
                    maskedText = pattern.Replace(maskedText, match =>
                    {
                        // 保留前缀和部分字符，其余用 * 替换
                        var originalMatch = match.Value;
                        if (originalMatch.Length <= 8)
                        {
                            return new string('*', originalMatch.Length);
                        }

                        var visibleChars = Math.Min(4, originalMatch.Length / 4);
                        var prefix = originalMatch.Substring(0, visibleChars);
                        var suffix = originalMatch.Substring(originalMatch.Length - visibleChars);
                        var maskLength = originalMatch.Length - (visibleChars * 2);
                        
                        return $"{prefix}{new string('*', maskLength)}{suffix}";
                    });
                }

                // 特殊处理：遮蔽长字符串中可能的密钥
                maskedText = Regex.Replace(maskedText, @"\b[a-zA-Z0-9_\-]{32,}\b", match =>
                {
                    var value = match.Value;
                    if (value.Length > 32)
                    {
                        return $"{value.Substring(0, 4)}***{value.Substring(value.Length - 4)}";
                    }
                    return value;
                }, RegexOptions.Compiled);
            }
            catch (Exception ex)
            {
                // 如果过滤过程中出现异常，记录日志但返回原文本
                Microsoft.VisualStudio.Shell.ActivityLog.LogError("SensitiveDataMasker", $"数据过滤失败: {ex.Message}");
                return text;
            }

            return maskedText;
        }

        /// <summary>
        /// 检查文本是否包含敏感数据
        /// </summary>
        /// <param name="text">要检查的文本</param>
        /// <returns>如果包含敏感数据返回 true</returns>
        public static bool ContainsSensitiveData(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            try
            {
                foreach (var pattern in SensitivePatterns)
                {
                    if (pattern.IsMatch(text))
                        return true;
                }
            }
            catch (Exception ex)
            {
                Microsoft.VisualStudio.Shell.ActivityLog.LogError("SensitiveDataMasker", $"敏感数据检查失败: {ex.Message}");
            }

            return false;
        }
    }
}
