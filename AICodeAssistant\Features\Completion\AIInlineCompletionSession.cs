using System;
using System.Threading;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using Microsoft.VisualStudio.Text;
using Microsoft.VisualStudio.Text.Editor;
using Microsoft.VisualStudio.Shell;
using AICodeAssistant.Services;
using AICodeAssistant.UI;
using AICodeAssistant.Resources;

namespace AICodeAssistant.Features.Completion
{
    /// <summary>
    /// AI 内联补全会话，管理单个文本视图的补全状态
    /// </summary>
    public class AIInlineCompletionSession : IDisposable
    {
        private readonly ITextView _textView;
        private readonly ILlmProvider _llmProvider;
        private readonly CompletionSettings _settings;
        private readonly object _lockObject = new object();
        
        private CancellationTokenSource _currentRequestCancellation;
        private string _lastCompletionText;
        private SnapshotPoint? _lastCompletionPosition;
        private DateTime _lastRequestTime = DateTime.MinValue;
        
        // 防抖动设置
        private const int DebounceDelayMs = 500;

        public AIInlineCompletionSession(ITextView textView, ILlmProvider llmProvider, CompletionSettings settings)
        {
            _textView = textView ?? throw new ArgumentNullException(nameof(textView));
            _llmProvider = llmProvider ?? throw new ArgumentNullException(nameof(llmProvider));
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
        }

        /// <summary>
        /// 获取补全建议
        /// </summary>
        public async Task<IInlineCompletionResult> GetCompletionAsync(SnapshotPoint triggerPoint, CancellationToken cancellationToken)
        {
            try
            {
                lock (_lockObject)
                {
                    // 取消之前的请求
                    _currentRequestCancellation?.Cancel();
                    _currentRequestCancellation = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                }

                var requestToken = _currentRequestCancellation.Token;

                // 防抖动：如果请求太频繁，等待一段时间
                var timeSinceLastRequest = DateTime.Now - _lastRequestTime;
                if (timeSinceLastRequest.TotalMilliseconds < DebounceDelayMs)
                {
                    var delayTime = DebounceDelayMs - (int)timeSinceLastRequest.TotalMilliseconds;
                    await Task.Delay(delayTime, requestToken);
                }

                _lastRequestTime = DateTime.Now;

                // 检查是否已取消
                if (requestToken.IsCancellationRequested)
                {
                    return null;
                }

                // 提取代码上下文
                var context = await ExtractContextAsync(triggerPoint, requestToken);
                if (context == null || requestToken.IsCancellationRequested)
                {
                    return null;
                }

                // 构建提示词
                var prompt = BuildCompletionPrompt(context);
                
                ActivityLog.LogInformation("AIInlineCompletionSession", $"发送内联补全请求，提示词长度: {prompt.Length}");

                // 调用 AI 生成补全
                var response = await _llmProvider.SendAsync(prompt, false, null, requestToken);
                
                if (string.IsNullOrWhiteSpace(response) || requestToken.IsCancellationRequested)
                {
                    return null;
                }

                // 清理和处理响应
                var cleanedResponse = CleanAiResponse(response);
                if (string.IsNullOrWhiteSpace(cleanedResponse))
                {
                    return null;
                }

                ActivityLog.LogInformation("AIInlineCompletionSession", $"收到内联补全: '{cleanedResponse}'");

                // 缓存结果
                _lastCompletionText = cleanedResponse;
                _lastCompletionPosition = triggerPoint;

                // 创建适用范围
                var applicableToSpan = new SnapshotSpan(triggerPoint, 0);

                return new InlineCompletionResult(cleanedResponse, applicableToSpan);
            }
            catch (OperationCanceledException)
            {
                ActivityLog.LogInformation("AIInlineCompletionSession", "内联补全请求被取消");
                return null;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AIInlineCompletionSession", $"内联补全失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 提取代码上下文
        /// </summary>
        private async Task<CodeContext> ExtractContextAsync(SnapshotPoint triggerPoint, CancellationToken cancellationToken)
        {
            try
            {
                // 使用现有的上下文提取器
                var context = await ContextExtractor.ExtractContextAsync(_textView, _settings.ContextLines);
                
                if (context != null)
                {
                    ActivityLog.LogInformation("AIInlineCompletionSession", 
                        $"上下文提取成功 - 文件: {context.FilePath}, 光标前: {context.BeforeCaret?.Length ?? 0} 字符");
                }

                return context;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AIInlineCompletionSession", $"上下文提取失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 构建补全提示词
        /// </summary>
        private string BuildCompletionPrompt(CodeContext context)
        {
            try
            {
                var language = GetLanguageFromFilePath(context.FilePath);
                
                var systemPrompt = PromptTemplates.ReplaceTokens(
                    PromptTemplates.CodeCompletionSystem,
                    language,
                    context.FilePath);

                var userPrompt = PromptTemplates.ReplaceTokens(
                    PromptTemplates.CodeCompletionUser,
                    language,
                    context.FilePath,
                    ("{BEFORE_CURSOR}", context.BeforeCaret ?? string.Empty),
                    ("{AFTER_CURSOR}", context.AfterCaret ?? string.Empty));

                return $"{systemPrompt}\n\n{userPrompt}";
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AIInlineCompletionSession", $"构建提示词失败: {ex.Message}");
                return $"Complete this code:\n{context.BeforeCaret ?? string.Empty}";
            }
        }

        /// <summary>
        /// 清理 AI 响应
        /// </summary>
        private string CleanAiResponse(string response)
        {
            if (string.IsNullOrWhiteSpace(response))
                return string.Empty;

            // 移除代码块标记
            response = Regex.Replace(response, @"```[\w]*\n?", "", RegexOptions.IgnoreCase);
            response = Regex.Replace(response, @"```", "", RegexOptions.IgnoreCase);

            // 移除常见的前缀
            var prefixesToRemove = new[]
            {
                "Here's the completion:",
                "The completion is:",
                "Complete code:",
                "Completion:",
                "Result:",
                "Output:",
                "Answer:",
                "Code:"
            };

            foreach (var prefix in prefixesToRemove)
            {
                if (response.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
                {
                    response = response.Substring(prefix.Length).TrimStart();
                    break;
                }
            }

            // 只取第一行（内联补全通常是单行的）
            var lines = response.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
            if (lines.Length > 0)
            {
                response = lines[0].Trim();
            }

            // 移除引号（如果整个响应被引号包围）
            if (response.StartsWith("\"") && response.EndsWith("\""))
            {
                response = response.Substring(1, response.Length - 2);
            }

            // 限制长度（内联补全不应该太长）
            if (response.Length > 200)
            {
                response = response.Substring(0, 200);
            }

            return response.Trim();
        }

        /// <summary>
        /// 从文件路径获取编程语言
        /// </summary>
        private string GetLanguageFromFilePath(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return "text";

            var extension = System.IO.Path.GetExtension(filePath)?.ToLowerInvariant();
            return extension switch
            {
                ".cs" => "csharp",
                ".vb" => "vb",
                ".cpp" or ".c" or ".h" or ".hpp" => "cpp",
                ".js" => "javascript",
                ".ts" => "typescript",
                ".py" => "python",
                ".java" => "java",
                ".xml" or ".xaml" => "xml",
                ".json" => "json",
                ".html" => "html",
                ".css" => "css",
                ".sql" => "sql",
                _ => "text"
            };
        }

        /// <summary>
        /// 获取最后的补全文本（用于调试）
        /// </summary>
        public string GetLastCompletionText()
        {
            return _lastCompletionText;
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public void Dispose()
        {
            lock (_lockObject)
            {
                _currentRequestCancellation?.Cancel();
                _currentRequestCancellation?.Dispose();
                _currentRequestCancellation = null;
            }
        }
    }
}
