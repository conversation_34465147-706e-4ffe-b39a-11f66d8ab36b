using System;
using System.IO;
using System.Text.Json;
using Microsoft.VisualStudio.Shell;
using AICodeAssistant.Services;
using AICodeAssistant.UI;

namespace AICodeAssistant.Tests
{
    /// <summary>
    /// 代码补全功能诊断工具
    /// </summary>
    public static class CompletionDiagnostics
    {
        /// <summary>
        /// 运行完整的诊断检查
        /// </summary>
        public static void RunDiagnostics()
        {
            Console.WriteLine("=== AI 代码补全功能诊断 ===\n");

            try
            {
                // 1. 检查设置文件
                CheckSettingsFile();
                Console.WriteLine();

                // 2. 检查 LLM 提供者
                CheckLlmProviders();
                Console.WriteLine();

                // 3. 检查 MEF 导出
                CheckMefExports();
                Console.WriteLine();

                // 4. 检查常量定义
                CheckConstants();
                Console.WriteLine();

                // 5. 检查文件类型支持
                CheckFileTypeSupport();
                Console.WriteLine();

                Console.WriteLine("✅ 诊断完成！");
                Console.WriteLine("\n如果补全功能仍未工作，请检查：");
                Console.WriteLine("1. Visual Studio 活动日志中的错误信息");
                Console.WriteLine("2. 确保 Ollama 服务正在运行（如果使用 Ollama）");
                Console.WriteLine("3. 确保 OpenAI API 密钥正确（如果使用 OpenAI）");
                Console.WriteLine("4. 尝试重启 Visual Studio");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 诊断过程中出现错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 检查设置文件
        /// </summary>
        private static void CheckSettingsFile()
        {
            Console.WriteLine("📋 检查设置文件...");

            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var folderPath = Path.Combine(appDataPath, Constants.Config.AppDataFolder);
                var filePath = Path.Combine(folderPath, Constants.Config.SettingsFileName);

                Console.WriteLine($"设置文件路径: {filePath}");

                if (File.Exists(filePath))
                {
                    Console.WriteLine("✓ 设置文件存在");
                    
                    var json = File.ReadAllText(filePath);
                    var settings = JsonSerializer.Deserialize<AppSettings>(json);
                    
                    if (settings != null)
                    {
                        Console.WriteLine("✓ 设置文件格式正确");
                        Console.WriteLine($"  - 补全功能启用: {settings.Completion.Enabled}");
                        Console.WriteLine($"  - 提供者类型: {settings.ProviderType}");
                        Console.WriteLine($"  - 上下文行数: {settings.Completion.ContextLines}");
                        Console.WriteLine($"  - 最大 Token 数: {settings.Completion.MaxTokens}");
                        Console.WriteLine($"  - 温度参数: {settings.Completion.Temperature}");

                        if (settings.ProviderType == LlmProviderType.Ollama)
                        {
                            Console.WriteLine($"  - Ollama API 地址: {settings.Ollama.ApiBase}");
                            Console.WriteLine($"  - Ollama 模型: {settings.Ollama.Model}");
                        }
                        else if (settings.ProviderType == LlmProviderType.OpenAI)
                        {
                            Console.WriteLine($"  - OpenAI API 地址: {settings.OpenAI.ApiBase}");
                            Console.WriteLine($"  - OpenAI 模型: {settings.OpenAI.Model}");
                            Console.WriteLine($"  - API 密钥已设置: {!string.IsNullOrEmpty(settings.OpenAI.ApiKey)}");
                        }
                    }
                    else
                    {
                        Console.WriteLine("❌ 设置文件格式错误");
                    }
                }
                else
                {
                    Console.WriteLine("⚠️ 设置文件不存在，将使用默认设置");
                    var defaultSettings = AppSettings.CreateDefault();
                    Console.WriteLine($"  - 默认补全功能启用: {defaultSettings.Completion.Enabled}");
                    Console.WriteLine($"  - 默认提供者类型: {defaultSettings.ProviderType}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 检查设置文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查 LLM 提供者
        /// </summary>
        private static void CheckLlmProviders()
        {
            Console.WriteLine("🤖 检查 LLM 提供者...");

            try
            {
                // 检查 Ollama 提供者
                Console.WriteLine("检查 Ollama 提供者:");
                var ollamaOptions = LlmProviderFactory.GetDefaultOptions(LlmProviderType.Ollama);
                var ollamaProvider = LlmProviderFactory.CreateProvider(LlmProviderType.Ollama, ollamaOptions);
                
                Console.WriteLine($"  - 提供者名称: {ollamaProvider.Name}");
                Console.WriteLine($"  - 可用状态: {ollamaProvider.IsAvailable}");
                Console.WriteLine($"  - API 地址: {ollamaOptions.ApiBase}");
                Console.WriteLine($"  - 默认模型: {ollamaOptions.Model}");

                // 检查 OpenAI 提供者
                Console.WriteLine("\n检查 OpenAI 提供者:");
                var openaiOptions = LlmProviderFactory.GetDefaultOptions(LlmProviderType.OpenAI);
                var openaiProvider = LlmProviderFactory.CreateProvider(LlmProviderType.OpenAI, openaiOptions);
                
                Console.WriteLine($"  - 提供者名称: {openaiProvider.Name}");
                Console.WriteLine($"  - API 地址: {openaiOptions.ApiBase}");
                Console.WriteLine($"  - 默认模型: {openaiOptions.Model}");
                Console.WriteLine($"  - 注意: OpenAI 需要有效的 API 密钥才能使用");

                Console.WriteLine("✓ LLM 提供者检查完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 检查 LLM 提供者失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查 MEF 导出
        /// </summary>
        private static void CheckMefExports()
        {
            Console.WriteLine("🔌 检查 MEF 导出...");

            try
            {
                Console.WriteLine("AICompletionSourceProvider 导出属性:");
                Console.WriteLine("  - [Export(typeof(IAsyncCompletionSourceProvider))] ✓");
                Console.WriteLine("  - [Name(\"AI Code Completion\")] ✓");
                Console.WriteLine("  - 支持的内容类型:");
                Console.WriteLine("    - CSharp ✓");
                Console.WriteLine("    - Basic ✓");
                Console.WriteLine("    - C/C++ ✓");
                Console.WriteLine("    - JavaScript ✓");
                Console.WriteLine("    - TypeScript ✓");
                Console.WriteLine("    - Python ✓");
                Console.WriteLine("    - Java ✓");
                Console.WriteLine("    - XML ✓");
                Console.WriteLine("    - JSON ✓");
                Console.WriteLine("    - HTML ✓");
                Console.WriteLine("    - CSS ✓");
                Console.WriteLine("    - SQL ✓");
                Console.WriteLine("    - text ✓");

                Console.WriteLine("✓ MEF 导出配置正确");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 检查 MEF 导出失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查常量定义
        /// </summary>
        private static void CheckConstants()
        {
            Console.WriteLine("📊 检查常量定义...");

            try
            {
                Console.WriteLine("补全相关常量:");
                Console.WriteLine($"  - 触发字符数量: {Constants.Completion.TriggerCharacters.Length}");
                Console.WriteLine($"  - 触发字符: {string.Join(", ", Constants.Completion.TriggerCharacters)}");
                Console.WriteLine($"  - 最小触发长度: {Constants.Completion.MinTriggerLength}");
                Console.WriteLine($"  - 最大补全项: {Constants.Completion.MaxCompletionItems}");
                Console.WriteLine($"  - 高优先级: {Constants.Completion.HighPriority}");
                Console.WriteLine($"  - 中优先级: {Constants.Completion.MediumPriority}");
                Console.WriteLine($"  - 低优先级: {Constants.Completion.LowPriority}");

                Console.WriteLine("\n补全类型:");
                Console.WriteLine($"  - 方法: {Constants.Completion.TypeMethod}");
                Console.WriteLine($"  - 属性: {Constants.Completion.TypeProperty}");
                Console.WriteLine($"  - 变量: {Constants.Completion.TypeVariable}");
                Console.WriteLine($"  - 类: {Constants.Completion.TypeClass}");
                Console.WriteLine($"  - 关键字: {Constants.Completion.TypeKeyword}");
                Console.WriteLine($"  - 代码片段: {Constants.Completion.TypeSnippet}");

                Console.WriteLine("✓ 常量定义检查完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 检查常量定义失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查文件类型支持
        /// </summary>
        private static void CheckFileTypeSupport()
        {
            Console.WriteLine("📁 检查文件类型支持...");

            try
            {
                Console.WriteLine("支持的文件扩展名:");
                foreach (var ext in Constants.FileExtensions.SupportedCodeFiles)
                {
                    Console.WriteLine($"  - {ext}");
                }

                Console.WriteLine($"\n总计支持 {Constants.FileExtensions.SupportedCodeFiles.Length} 种文件类型");
                Console.WriteLine("✓ 文件类型支持检查完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 检查文件类型支持失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录诊断信息到活动日志
        /// </summary>
        public static void LogDiagnosticsToActivityLog()
        {
            try
            {
                ActivityLog.LogInformation("CompletionDiagnostics", "开始记录补全功能诊断信息");
                
                // 记录设置状态
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var settingsPath = Path.Combine(appDataPath, Constants.Config.AppDataFolder, Constants.Config.SettingsFileName);
                ActivityLog.LogInformation("CompletionDiagnostics", $"设置文件路径: {settingsPath}");
                ActivityLog.LogInformation("CompletionDiagnostics", $"设置文件存在: {File.Exists(settingsPath)}");

                // 记录常量状态
                ActivityLog.LogInformation("CompletionDiagnostics", $"触发字符: {string.Join(",", Constants.Completion.TriggerCharacters)}");
                ActivityLog.LogInformation("CompletionDiagnostics", $"最小触发长度: {Constants.Completion.MinTriggerLength}");
                ActivityLog.LogInformation("CompletionDiagnostics", $"最大补全项: {Constants.Completion.MaxCompletionItems}");

                ActivityLog.LogInformation("CompletionDiagnostics", "诊断信息记录完成");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("CompletionDiagnostics", $"记录诊断信息失败: {ex.Message}");
            }
        }
    }
}
