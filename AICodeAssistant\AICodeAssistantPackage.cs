﻿global using System;
global using Community.VisualStudio.Toolkit;
global using Microsoft.VisualStudio.Shell;
global using Task = System.Threading.Tasks.Task;
using System.Runtime.InteropServices;
using System.Threading;
using Microsoft.VisualStudio.ComponentModelHost;
using System.ComponentModel.Composition.Hosting;
using System.Linq;

namespace AICodeAssistant
{
    [PackageRegistration(UseManagedResourcesOnly = true, AllowsBackgroundLoading = true)]
    [InstalledProductRegistration(Vsix.Name, Vsix.Description, Vsix.Version)]
    [ProvideToolWindow(typeof(ChatToolWindow.Pane), Style = VsDockStyle.Tabbed, Window = WindowGuids.SolutionExplorer)]
    [ProvideMenuResource("Menus.ctmenu", 1)]
    [Guid(PackageGuids.AICodeAssistantString)]
    public sealed class AICodeAssistantPackage : ToolkitPackage
    {
        protected override async Task InitializeAsync(CancellationToken cancellationToken, IProgress<ServiceProgressData> progress)
        {
            // 切换到主线程
            await JoinableTaskFactory.SwitchToMainThreadAsync(cancellationToken);

            try
            {
                // 记录包初始化开始
                ActivityLog.LogInformation("AICodeAssistantPackage", "开始初始化 AI Code Assistant 包");

                // 注册命令
                await this.RegisterCommandsAsync();

                // 注册工具窗口
                this.RegisterToolWindows();

                // 确保 MEF 组件被加载
                await EnsureMefComponentsLoadedAsync();

                ActivityLog.LogInformation("AICodeAssistantPackage", "AI Code Assistant 包初始化完成");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AICodeAssistantPackage", $"包初始化失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 确保 MEF 组件被正确加载
        /// </summary>
        private async Task EnsureMefComponentsLoadedAsync()
        {
            try
            {
                // 获取组件模型服务
                var componentModel = await GetServiceAsync(typeof(SComponentModel)) as IComponentModel;
                if (componentModel != null)
                {
                    // 获取导出提供者
                    var exportProvider = componentModel.DefaultExportProvider;

                    // 尝试获取我们的补全源提供者以确保它被加载
                    var completionProviders = exportProvider.GetExports<Microsoft.VisualStudio.Language.Intellisense.AsyncCompletion.IAsyncCompletionSourceProvider>();

                    ActivityLog.LogInformation("AICodeAssistantPackage", $"找到 {completionProviders.Count()} 个补全源提供者");

                    // 检查我们的提供者是否在其中
                    var ourProvider = completionProviders.FirstOrDefault(p =>
                        p.Value.GetType().Name == "AICompletionSourceProvider");

                    if (ourProvider != null)
                    {
                        ActivityLog.LogInformation("AICodeAssistantPackage", "AI 补全源提供者已成功加载");
                    }
                    else
                    {
                        ActivityLog.LogWarning("AICodeAssistantPackage", "未找到 AI 补全源提供者");
                    }
                }
                else
                {
                    ActivityLog.LogError("AICodeAssistantPackage", "无法获取组件模型服务");
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AICodeAssistantPackage", $"检查 MEF 组件失败: {ex.Message}");
            }
        }
    }
}