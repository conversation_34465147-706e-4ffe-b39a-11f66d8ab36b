using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using Microsoft.VisualStudio.Shell;
using Microsoft.VisualStudio.Text.Editor;
using AICodeAssistant.Services;
using AICodeAssistant.Resources;
using AICodeAssistant.Features.SideChat;
using AICodeAssistant.UI;

namespace AICodeAssistant
{
    public partial class ChatToolWindowControl : UserControl
    {
        private readonly List<ChatMessage> _chatHistory = new List<ChatMessage>();
        private ILlmProvider _currentProvider;
        private CancellationTokenSource _currentCancellation;
        private string _currentFilePath = string.Empty;
        private string _selectedCode = string.Empty;
        private const string PlaceholderText = "输入您的问题或选择代码后提问...";
        private bool _isPlaceholderActive = true;
        private bool _isEditMode = false;
        private ChatMessage _editingMessage = null;
        private int _currentHistoryTokens = 0;
        private int _currentContextTokens = 0;
        private int _maxContextTokens = 8192;

        public ChatToolWindowControl()
        {
            InitializeComponent();
            InitializeAsync().FireAndForget();
        }

        private async Task InitializeAsync()
        {
            try
            {
                // 设置默认提供者
                ProviderComboBox.SelectedIndex = 0; // 默认选择 Ollama
                await UpdateProviderAsync().ConfigureAwait(false);

                // 加载聊天历史
                await LoadChatHistoryAsync().ConfigureAwait(false);

                // 监听当前文件变化
                await MonitorCurrentFileAsync().ConfigureAwait(false);

                // 初始化 Token 统计
                await UpdateTokenStatsAsync().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("ChatToolWindowControl", $"初始化失败: {ex.Message}");
            }
        }

        private async Task UpdateProviderAsync()
        {
            try
            {
                // 从设置文件加载配置
                var settings = await LoadAppSettingsAsync().ConfigureAwait(false);
                ActivityLog.LogInformation("ChatToolWindowControl", $"加载设置完成 - 提供者: {settings.ProviderType}");

                var options = CreateLlmOptionsFromSettings(settings);

                // 详细记录配置信息
                if (settings.ProviderType == LlmProviderType.Ollama)
                {
                    ActivityLog.LogInformation("ChatToolWindowControl", $"Ollama 配置 - API Base: {options.ApiBase}, Model: {options.Model}, Temperature: {options.Temperature}, MaxTokens: {options.MaxTokens}");
                }
                else
                {
                    ActivityLog.LogInformation("ChatToolWindowControl", $"OpenAI 配置 - API Base: {options.ApiBase}, Model: {options.Model}, Temperature: {options.Temperature}, MaxTokens: {options.MaxTokens}");
                }

                _currentProvider = LlmProviderFactory.CreateProvider(settings.ProviderType, options);
                ActivityLog.LogInformation("ChatToolWindowControl", $"成功创建 {settings.ProviderType} 提供者");

                // 更新UI显示
                await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                ProviderComboBox.SelectedIndex = settings.ProviderType == LlmProviderType.OpenAI ? 1 : 0;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("ChatToolWindowControl", $"更新提供者失败: {ex.Message}");
                ActivityLog.LogError("ChatToolWindowControl", $"完整错误信息: {ex}");
            }
        }

        private Task<AppSettings> LoadAppSettingsAsync()
        {
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var folderPath = Path.Combine(appDataPath, Constants.Config.AppDataFolder);
                var filePath = Path.Combine(folderPath, Constants.Config.SettingsFileName);

                if (File.Exists(filePath))
                {
                    var json = File.ReadAllText(filePath);
                    return Task.FromResult(JsonSerializer.Deserialize<AppSettings>(json) ?? AppSettings.CreateDefault());
                }
                else
                {
                    return Task.FromResult(AppSettings.CreateDefault());
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("ChatToolWindowControl", $"加载设置失败: {ex.Message}");
                return Task.FromResult(AppSettings.CreateDefault());
            }
        }

        private LlmOptions CreateLlmOptionsFromSettings(AppSettings settings)
        {
            var options = settings.ProviderType switch
            {
                LlmProviderType.OpenAI => new LlmOptions
                {
                    ApiKey = settings.OpenAI.ApiKey,
                    ApiBase = settings.OpenAI.ApiBase,
                    Model = settings.OpenAI.Model,
                    Temperature = settings.Completion.Temperature,
                    MaxTokens = settings.Chat.MaxOutputTokens, // 使用聊天设置中的最大输出token数
                    TimeoutSeconds = 30
                },
                LlmProviderType.Ollama => new LlmOptions
                {
                    ApiBase = string.IsNullOrWhiteSpace(settings.Ollama.ApiBase) ? Constants.Ollama.DefaultApiBase : settings.Ollama.ApiBase,
                    Model = string.IsNullOrWhiteSpace(settings.Ollama.Model) ? Constants.Defaults.DefaultOllamaModel : settings.Ollama.Model,
                    Temperature = settings.Completion.Temperature,
                    MaxTokens = settings.Chat.MaxOutputTokens, // 使用聊天设置中的最大输出token数
                    TimeoutSeconds = 60
                },
                _ => LlmProviderFactory.GetDefaultOptions(LlmProviderType.Ollama)
            };

            // 验证关键配置
            if (settings.ProviderType == LlmProviderType.Ollama)
            {
                if (string.IsNullOrWhiteSpace(options.ApiBase))
                {
                    ActivityLog.LogWarning("ChatToolWindowControl", "Ollama API Base 为空，使用默认值");
                    options.ApiBase = Constants.Ollama.DefaultApiBase;
                }

                if (string.IsNullOrWhiteSpace(options.Model))
                {
                    ActivityLog.LogWarning("ChatToolWindowControl", "Ollama Model 为空，使用默认值");
                    options.Model = Constants.Defaults.DefaultOllamaModel;
                }

                // 确保 API Base 格式正确
                if (!options.ApiBase.StartsWith("http://") && !options.ApiBase.StartsWith("https://"))
                {
                    ActivityLog.LogWarning("ChatToolWindowControl", $"API Base 格式可能不正确: {options.ApiBase}");
                }
            }

            return options;
        }

        private async Task MonitorCurrentFileAsync()
        {
            try
            {
                await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();

                // 获取当前活动的文档
                var dte = await VS.GetServiceAsync<EnvDTE.DTE, EnvDTE.DTE>().ConfigureAwait(false);
                if (dte?.ActiveDocument != null)
                {
                    _currentFilePath = dte.ActiveDocument.FullName;
                    CurrentFileLabel.Text = $"当前文件: {Path.GetFileName(_currentFilePath)}";

                    // 获取选中的代码
                    try
                    {
                        if (!string.IsNullOrEmpty(_currentFilePath))
                        {
                            var documentView = await VS.Documents.GetDocumentViewAsync(_currentFilePath).ConfigureAwait(false);
                            if (documentView?.TextView?.Selection != null && !documentView.TextView.Selection.IsEmpty)
                            {
                                _selectedCode = documentView.TextView.Selection.StreamSelectionSpan.GetText();
                            }
                        }
                    }
                    catch
                    {
                        // 如果获取选中代码失败，忽略错误
                        _selectedCode = string.Empty;
                    }
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("ChatToolWindowControl", $"监控当前文件失败: {ex.Message}");
            }
        }

        private void SendButton_Click(object sender, RoutedEventArgs e)
        {
            _ = SendMessageAsync();
        }

        private void EditSendButton_Click(object sender, RoutedEventArgs e)
        {
            _ = ResendEditedMessageAsync();
        }

        private void InlineEditSendButton_Click(object sender, RoutedEventArgs e)
        {
            _ = ResendInlineEditedMessageAsync(sender as Button);
        }

        private void CancelEditButton_Click(object sender, RoutedEventArgs e)
        {
            ExitEditMode();
        }

        private void InputTextBox_PreviewKeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                if (Keyboard.Modifiers == ModifierKeys.Control)
                {
                    // Ctrl+Enter 插入换行
                    var textBox = sender as TextBox;
                    if (textBox != null)
                    {
                        var caretIndex = textBox.CaretIndex;
                        textBox.Text = textBox.Text.Insert(caretIndex, Environment.NewLine);
                        textBox.CaretIndex = caretIndex + Environment.NewLine.Length;
                    }
                    e.Handled = true;
                }
                else
                {
                    // 单独按 Enter 发送消息
                    _ = SendMessageAsync();
                    e.Handled = true;
                }
            }
            else if (e.Key == Key.Escape && _isEditMode)
            {
                // Escape 键退出编辑模式
                ExitEditMode();
                e.Handled = true;
            }
        }

        private void InputTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            // 保留原有的KeyDown处理器作为备用
            // PreviewKeyDown应该已经处理了大部分情况
        }

        private void InputTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (_isPlaceholderActive)
            {
                InputTextBox.Text = string.Empty;
                InputTextBox.Foreground = Brushes.White;
                _isPlaceholderActive = false;
            }
        }

        private void InputTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(InputTextBox.Text))
            {
                InputTextBox.Text = PlaceholderText;
                InputTextBox.Foreground = Brushes.Gray;
                _isPlaceholderActive = true;
            }
        }

        private async Task SendMessageAsync()
        {
            // 如果在编辑模式，调用重新发送方法
            if (_isEditMode)
            {
                await ResendEditedMessageAsync().ConfigureAwait(false);
                return;
            }

            var userMessage = InputTextBox.Text?.Trim();
            if (string.IsNullOrEmpty(userMessage) || _isPlaceholderActive)
                return;

            try
            {
                // 清空输入框并重置占位符，重新获得焦点
                InputTextBox.Text = string.Empty;
                InputTextBox.Foreground = Brushes.White;
                _isPlaceholderActive = false;
                InputTextBox.Focus();

                // 取消之前的请求
                _currentCancellation?.Cancel();
                _currentCancellation = new CancellationTokenSource();

                // 添加用户消息到聊天历史
                var userChatMessage = new ChatMessage
                {
                    Role = "user",
                    Content = userMessage,
                    Timestamp = DateTime.Now
                };
                _chatHistory.Add(userChatMessage);
                AddMessageToUI(userChatMessage);

                // 检查是否是命令
                if (userMessage.StartsWith("/"))
                {
                    await HandleSlashCommandAsync(userMessage, _currentCancellation.Token).ConfigureAwait(false);
                    return;
                }

                // 显示正在输入指示器
                var typingMessage = new ChatMessage
                {
                    Role = "assistant",
                    Content = Constants.UI.LoadingText,
                    Timestamp = DateTime.Now,
                    IsTyping = true
                };
                AddMessageToUI(typingMessage);

                // 构建包含上下文的提示词（使用 messages 格式）
                var prompt = await BuildChatMessagesWithContextAsync(userMessage).ConfigureAwait(false);
                ActivityLog.LogInformation("ChatToolWindowControl", $"构建的提示词长度: {prompt.Length} 字符");

                // 检查提供者是否可用
                if (_currentProvider == null)
                {
                    ActivityLog.LogError("ChatToolWindowControl", "当前提供者为空，重新初始化");
                    await UpdateProviderAsync().ConfigureAwait(false);

                    if (_currentProvider == null)
                    {
                        throw new InvalidOperationException("无法初始化 LLM 提供者");
                    }
                }

                ActivityLog.LogInformation("ChatToolWindowControl", $"使用提供者: {_currentProvider.Name}，开始发送请求");

                // 发送到 LLM
                var response = await _currentProvider.SendAsync(
                    prompt,
                    true,
                    new Progress<string>(chunk =>
                    {
                        ThreadHelper.JoinableTaskFactory.Run(async () =>
                        {
                            await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                            UpdateTypingMessage(chunk);
                        });
                    }),
                    _currentCancellation.Token).ConfigureAwait(false);

                ActivityLog.LogInformation("ChatToolWindowControl", $"收到响应，长度: {response?.Length ?? 0} 字符");

                // 移除正在输入指示器并添加完整响应
                RemoveTypingMessage();
                var assistantMessage = new ChatMessage
                {
                    Role = "assistant",
                    Content = response,
                    Timestamp = DateTime.Now
                };
                _chatHistory.Add(assistantMessage);
                AddMessageToUI(assistantMessage);

                // 保存聊天历史
                await SaveChatHistoryAsync().ConfigureAwait(false);

                // 更新 Token 统计
                await UpdateTokenStatsAsync().ConfigureAwait(false);
            }
            catch (OperationCanceledException)
            {
                RemoveTypingMessage();
                ResetInputBoxToPlaceholder();
            }
            catch (Exception ex)
            {
                RemoveTypingMessage();
                ResetInputBoxToPlaceholder();

                // 详细的错误分析和日志记录
                string errorDetails = AnalyzeError(ex);
                ActivityLog.LogError("ChatToolWindowControl", $"发送消息失败: {ex.Message}");
                ActivityLog.LogError("ChatToolWindowControl", $"完整错误信息: {ex}");
                ActivityLog.LogError("ChatToolWindowControl", $"错误分析: {errorDetails}");

                var errorMessage = new ChatMessage
                {
                    Role = "assistant",
                    Content = $"错误: {ex.Message}\n\n{errorDetails}",
                    Timestamp = DateTime.Now,
                    IsError = true
                };
                AddMessageToUI(errorMessage);
            }
        }

        private async Task<string> BuildChatPromptAsync(string userMessage)
        {
            await MonitorCurrentFileAsync().ConfigureAwait(false);

            var language = GetLanguageFromFilePath(_currentFilePath);
            var systemPrompt = PromptTemplates.ReplaceTokens(
                PromptTemplates.ChatAssistantSystem,
                language,
                _currentFilePath);

            string userPrompt;
            if (!string.IsNullOrEmpty(_selectedCode) && _selectedCode.Length <= 5000) // 限制选中代码长度
            {
                userPrompt = PromptTemplates.ReplaceTokens(
                    PromptTemplates.ChatUserWithSelection,
                    language,
                    _currentFilePath,
                    ("{SELECTED_CODE}", _selectedCode),
                    ("{USER_MESSAGE}", userMessage));
            }
            else
            {
                userPrompt = PromptTemplates.ReplaceTokens(
                    PromptTemplates.ChatUserWithoutSelection,
                    language,
                    _currentFilePath,
                    ("{USER_MESSAGE}", userMessage));
            }

            var fullPrompt = $"{systemPrompt}\n\n{userPrompt}";
            return SensitiveDataMasker.MaskSensitiveData(fullPrompt);
        }

        /// <summary>
        /// 构建包含上下文的聊天提示词
        /// </summary>
        /// <param name="userMessage">用户消息</param>
        /// <returns>包含上下文的完整提示词</returns>
        private async Task<string> BuildChatPromptWithContextAsync(string userMessage)
        {
            await MonitorCurrentFileAsync().ConfigureAwait(false);

            var language = GetLanguageFromFilePath(_currentFilePath);
            var systemPrompt = PromptTemplates.ReplaceTokens(
                PromptTemplates.ChatAssistantSystem,
                language,
                _currentFilePath);

            // 获取设置中的最大上下文 Token 数
            var settings = await LoadAppSettingsAsync().ConfigureAwait(false);
            var maxContextTokens = settings.Chat.MaxContextLength;

            // 构建上下文历史
            var contextHistory = BuildContextHistory(maxContextTokens);

            string userPrompt;
            if (!string.IsNullOrEmpty(_selectedCode) && _selectedCode.Length <= 5000)
            {
                userPrompt = PromptTemplates.ReplaceTokens(
                    PromptTemplates.ChatUserWithSelection,
                    language,
                    _currentFilePath,
                    ("{SELECTED_CODE}", _selectedCode),
                    ("{USER_MESSAGE}", userMessage));
            }
            else
            {
                userPrompt = PromptTemplates.ReplaceTokens(
                    PromptTemplates.ChatUserWithoutSelection,
                    language,
                    _currentFilePath,
                    ("{USER_MESSAGE}", userMessage));
            }

            // 组合完整提示词：系统提示 + 上下文历史 + 当前用户消息
            var fullPrompt = $"{systemPrompt}\n\n{contextHistory}\n\n{userPrompt}";

            ActivityLog.LogInformation("ChatToolWindowControl", $"上下文历史长度: {contextHistory.Length} 字符");
            ActivityLog.LogInformation("ChatToolWindowControl", $"完整提示词长度: {fullPrompt.Length} 字符");
            ActivityLog.LogInformation("ChatToolWindowControl", $"估算总 Token 数: {EstimateTokenCount(fullPrompt)} tokens");

            return SensitiveDataMasker.MaskSensitiveData(fullPrompt);
        }

        /// <summary>
        /// 构建包含上下文的聊天消息（messages 格式）
        /// </summary>
        /// <param name="userMessage">用户消息</param>
        /// <returns>JSON 格式的 messages 数组</returns>
        private async Task<string> BuildChatMessagesWithContextAsync(string userMessage)
        {
            await MonitorCurrentFileAsync().ConfigureAwait(false);

            var language = GetLanguageFromFilePath(_currentFilePath);
            var systemPrompt = PromptTemplates.ReplaceTokens(
                PromptTemplates.ChatAssistantSystem,
                language,
                _currentFilePath);

            // 获取设置中的最大上下文 Token 数
            var settings = await LoadAppSettingsAsync().ConfigureAwait(false);
            var maxContextTokens = settings.Chat.MaxContextLength;

            // 构建 messages 数组
            var messages = new List<object>();

            // 添加系统消息
            messages.Add(new { role = "system", content = systemPrompt });

            // 添加历史对话消息
            var contextMessages = BuildContextMessages(maxContextTokens);
            messages.AddRange(contextMessages);

            // 构建当前用户消息
            string currentUserMessage;
            if (!string.IsNullOrEmpty(_selectedCode) && _selectedCode.Length <= 5000)
            {
                currentUserMessage = PromptTemplates.ReplaceTokens(
                    PromptTemplates.ChatUserWithSelection,
                    language,
                    _currentFilePath,
                    ("{SELECTED_CODE}", _selectedCode),
                    ("{USER_MESSAGE}", userMessage));
            }
            else
            {
                currentUserMessage = PromptTemplates.ReplaceTokens(
                    PromptTemplates.ChatUserWithoutSelection,
                    language,
                    _currentFilePath,
                    ("{USER_MESSAGE}", userMessage));
            }

            // 添加当前用户消息
            messages.Add(new { role = "user", content = currentUserMessage });

            // 序列化为 JSON
            var messagesJson = JsonSerializer.Serialize(new { messages }, new JsonSerializerOptions { WriteIndented = false });

            ActivityLog.LogInformation("ChatToolWindowControl", $"构建了 {messages.Count} 条消息");
            ActivityLog.LogInformation("ChatToolWindowControl", $"Messages JSON 长度: {messagesJson.Length} 字符");
            ActivityLog.LogInformation("ChatToolWindowControl", $"估算总 Token 数: {EstimateTokenCount(messagesJson)} tokens");

            return SensitiveDataMasker.MaskSensitiveData(messagesJson);
        }

        /// <summary>
        /// 构建上下文消息数组
        /// </summary>
        /// <param name="maxTokens">最大 Token 数</param>
        /// <returns>上下文消息列表</returns>
        private List<object> BuildContextMessages(int maxTokens)
        {
            var contextMessages = new List<object>();

            if (_chatHistory.Count <= 1) // 只有当前消息或没有历史
            {
                _currentContextTokens = 0;
                return contextMessages;
            }

            var totalTokens = 0;
            var totalHistoryTokens = 0;
            var truncated = false;

            // 计算所有历史消息的 Token 数
            for (int i = _chatHistory.Count - 2; i >= 0; i--)
            {
                var message = _chatHistory[i];
                if (message.IsTyping || message.IsError)
                {
                    continue;
                }
                var filteredContent = FilterThinkingContent(message.Content);
                totalHistoryTokens += EstimateTokenCount(filteredContent);
            }

            // 从最新的消息开始向前遍历（排除最后一条，因为那是当前要发送的）
            for (int i = _chatHistory.Count - 2; i >= 0; i--)
            {
                var message = _chatHistory[i];
                if (message.IsTyping || message.IsError)
                {
                    continue; // 跳过临时消息和错误消息
                }

                // 过滤思考过程内容，只保留正文用于上下文
                var filteredContent = FilterThinkingContent(message.Content);
                var messageTokens = EstimateTokenCount(filteredContent);

                // 检查是否会超过最大 Token 数
                if (totalTokens + messageTokens > maxTokens)
                {
                    truncated = true;
                    break;
                }

                // 在开头插入（因为我们是从后往前遍历）
                contextMessages.Insert(0, new { role = message.Role, content = filteredContent });
                totalTokens += messageTokens;
            }

            // 更新统计信息
            _currentHistoryTokens = totalHistoryTokens;
            _currentContextTokens = totalTokens;
            _maxContextTokens = maxTokens;

            // 显示截断提示
            ThreadHelper.JoinableTaskFactory.Run(async () =>
            {
                await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                ContextWarningPanel.Visibility = truncated ? Visibility.Visible : Visibility.Collapsed;
            });

            ActivityLog.LogInformation("ChatToolWindowControl", $"上下文消息使用了约 {totalTokens} tokens，历史总计 {totalHistoryTokens} tokens，是否截断: {truncated}");
            return contextMessages;
        }

        /// <summary>
        /// 更新 Token 统计显示
        /// </summary>
        private async Task UpdateTokenStatsAsync()
        {
            await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();

            var totalTokens = _currentHistoryTokens + _currentContextTokens;
            TokenStatsDisplay.Text = $"Token 统计: 历史 {_currentHistoryTokens} / 上下文 {_currentContextTokens} / 最大 {_maxContextTokens}";

            ActivityLog.LogInformation("ChatToolWindowControl", $"Token 统计更新: 历史={_currentHistoryTokens}, 上下文={_currentContextTokens}, 最大={_maxContextTokens}");
        }

        /// <summary>
        /// 构建上下文历史字符串
        /// </summary>
        /// <param name="maxTokens">最大 Token 数</param>
        /// <returns>上下文历史字符串</returns>
        private string BuildContextHistory(int maxTokens)
        {
            if (_chatHistory.Count <= 1) // 只有当前消息或没有历史
            {
                return string.Empty;
            }

            var contextBuilder = new StringBuilder();
            var totalTokens = 0;

            // 从最新的消息开始向前遍历（排除最后一条，因为那是当前要发送的）
            for (int i = _chatHistory.Count - 2; i >= 0; i--)
            {
                var message = _chatHistory[i];
                if (message.IsTyping || message.IsError)
                {
                    continue; // 跳过临时消息和错误消息
                }

                // 过滤思考过程内容，只保留正文用于上下文
                var filteredContent = FilterThinkingContent(message.Content);
                var messageText = $"{(message.Role == "user" ? "用户" : "助手")}: {filteredContent}\n\n";
                var messageTokens = EstimateTokenCount(messageText);

                // 检查是否会超过最大 Token 数
                if (totalTokens + messageTokens > maxTokens)
                {
                    break;
                }

                // 在开头插入（因为我们是从后往前遍历）
                contextBuilder.Insert(0, messageText);
                totalTokens += messageTokens;
            }

            var result = contextBuilder.ToString().Trim();
            if (!string.IsNullOrEmpty(result))
            {
                result = "=== 对话历史 ===\n" + result + "\n=== 当前对话 ===\n";
            }

            ActivityLog.LogInformation("ChatToolWindowControl", $"上下文历史使用了约 {totalTokens} tokens");
            return result;
        }

        /// <summary>
        /// 过滤思考过程内容，只保留正文用于上下文传递
        /// </summary>
        /// <param name="content">原始内容</param>
        /// <returns>过滤后的内容</returns>
        private string FilterThinkingContent(string content)
        {
            if (string.IsNullOrEmpty(content))
                return content;

            try
            {
                // 使用正则表达式移除 <think>...</think> 标签及其内容
                var thinkPattern = @"<think\s*>.*?</think\s*>";
                var filteredContent = System.Text.RegularExpressions.Regex.Replace(
                    content,
                    thinkPattern,
                    "",
                    System.Text.RegularExpressions.RegexOptions.Singleline |
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                // 清理多余的空行和空白字符
                filteredContent = System.Text.RegularExpressions.Regex.Replace(
                    filteredContent,
                    @"\n\s*\n\s*\n",
                    "\n\n");

                var result = filteredContent.Trim();

                // 记录过滤结果（仅在调试时）
                if (content != result)
                {
                    ActivityLog.LogInformation("ChatToolWindowControl",
                        $"过滤思考内容：原始长度 {content.Length} -> 过滤后长度 {result.Length}");
                }

                return result;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("ChatToolWindowControl", $"过滤思考内容失败: {ex.Message}");
                return content; // 如果过滤失败，返回原始内容
            }
        }

        /// <summary>
        /// 估算文本的 Token 数量
        /// 简单估算：英文约 4 字符 = 1 token，中文约 1.5 字符 = 1 token
        /// </summary>
        /// <param name="text">要估算的文本</param>
        /// <returns>估算的 Token 数</returns>
        private int EstimateTokenCount(string text)
        {
            if (string.IsNullOrEmpty(text))
                return 0;

            // 简单的 Token 估算算法
            // 对于混合中英文文本，使用平均值
            var charCount = text.Length;
            var estimatedTokens = (int)Math.Ceiling(charCount / 3.0); // 平均 3 字符 ≈ 1 token

            return estimatedTokens;
        }

        private string GetLanguageFromFilePath(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return "text";

            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension switch
            {
                ".cs" => "csharp",
                ".vb" => "vb",
                ".cpp" or ".cc" or ".cxx" => "cpp",
                ".c" => "c",
                ".js" => "javascript",
                ".ts" => "typescript",
                ".py" => "python",
                ".java" => "java",
                _ => "text"
            };
        }

        private async Task HandleSlashCommandAsync(string command, CancellationToken cancellationToken)
        {
            var parts = command.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            var cmd = parts[0].ToLowerInvariant();

            try
            {
                switch (cmd)
                {
                    case Constants.Commands.ClearCommand:
                        await ClearChatAsync().ConfigureAwait(false);
                        break;

                    case Constants.Commands.ExplainCommand:
                        if (!string.IsNullOrEmpty(_selectedCode))
                        {
                            await HandleExplainCommandAsync(_selectedCode, cancellationToken).ConfigureAwait(false);
                        }
                        else
                        {
                            AddSystemMessage("请先选择要解释的代码");
                        }
                        break;

                    case Constants.Commands.RefactorCommand:
                        if (!string.IsNullOrEmpty(_selectedCode))
                        {
                            await HandleRefactorCommandAsync(_selectedCode, cancellationToken).ConfigureAwait(false);
                        }
                        else
                        {
                            AddSystemMessage("请先选择要重构的代码");
                        }
                        break;

                    case Constants.Commands.TestCommand:
                        if (!string.IsNullOrEmpty(_selectedCode))
                        {
                            await HandleTestCommandAsync(_selectedCode, cancellationToken).ConfigureAwait(false);
                        }
                        else
                        {
                            AddSystemMessage("请先选择要生成测试的代码");
                        }
                        break;

                    case Constants.Commands.DocCommand:
                        if (!string.IsNullOrEmpty(_selectedCode))
                        {
                            await HandleDocCommandAsync(_selectedCode, cancellationToken).ConfigureAwait(false);
                        }
                        else
                        {
                            AddSystemMessage("请先选择要生成文档的代码");
                        }
                        break;

                    case Constants.Commands.SearchCommand:
                        var searchTerms = string.Join(" ", parts.Skip(1));
                        if (!string.IsNullOrEmpty(searchTerms))
                        {
                            await HandleSearchCommandAsync(searchTerms, cancellationToken).ConfigureAwait(false);
                        }
                        else
                        {
                            AddSystemMessage("请提供搜索关键词，例如: /search async await");
                        }
                        break;

                    default:
                        AddSystemMessage($"未知命令: {cmd}。可用命令: /explain, /refactor, /test, /doc, /search, /clear");
                        break;
                }
            }
            catch (Exception ex)
            {
                AddSystemMessage($"命令执行失败: {ex.Message}");
                ActivityLog.LogError("ChatToolWindowControl", $"命令执行失败: {ex.Message}");
            }
        }

        private async Task HandleExplainCommandAsync(string code, CancellationToken cancellationToken)
        {
            var language = GetLanguageFromFilePath(_currentFilePath);
            var prompt = PromptTemplates.ReplaceTokens(
                PromptTemplates.ExplainCodePrompt,
                language,
                additionalReplacements: new[] { ("{CODE}", code) });

            await SendLlmRequestAsync(prompt, cancellationToken).ConfigureAwait(false);
        }

        private async Task HandleRefactorCommandAsync(string code, CancellationToken cancellationToken)
        {
            var language = GetLanguageFromFilePath(_currentFilePath);
            var prompt = PromptTemplates.ReplaceTokens(
                PromptTemplates.RefactorCodePrompt,
                language,
                additionalReplacements: new[] { ("{CODE}", code) });

            await SendLlmRequestAsync(prompt, cancellationToken).ConfigureAwait(false);
        }

        private async Task HandleTestCommandAsync(string code, CancellationToken cancellationToken)
        {
            var language = GetLanguageFromFilePath(_currentFilePath);
            var prompt = PromptTemplates.ReplaceTokens(
                PromptTemplates.GenerateTestPrompt,
                language,
                additionalReplacements: new[] { ("{CODE}", code) });

            await SendLlmRequestAsync(prompt, cancellationToken).ConfigureAwait(false);
        }

        private async Task HandleDocCommandAsync(string code, CancellationToken cancellationToken)
        {
            var language = GetLanguageFromFilePath(_currentFilePath);
            var prompt = PromptTemplates.ReplaceTokens(
                PromptTemplates.GenerateDocPrompt,
                language,
                additionalReplacements: new[] { ("{CODE}", code) });

            await SendLlmRequestAsync(prompt, cancellationToken).ConfigureAwait(false);
        }

        private async Task HandleSearchCommandAsync(string searchTerms, CancellationToken cancellationToken)
        {
            var prompt = PromptTemplates.ReplaceTokens(
                PromptTemplates.SearchCodePrompt,
                additionalReplacements: new[]
                {
                    ("{SEARCH_TERMS}", searchTerms),
                    ("{FILE_PATH}", _currentFilePath)
                });

            await SendLlmRequestAsync(prompt, cancellationToken).ConfigureAwait(false);
        }

        private async Task SendLlmRequestAsync(string prompt, CancellationToken cancellationToken)
        {
            // 显示正在输入指示器
            var typingMessage = new ChatMessage
            {
                Role = "assistant",
                Content = Constants.UI.LoadingText,
                Timestamp = DateTime.Now,
                IsTyping = true
            };
            AddMessageToUI(typingMessage);

            try
            {
                var maskedPrompt = SensitiveDataMasker.MaskSensitiveData(prompt);
                var response = await _currentProvider.SendAsync(
                    maskedPrompt,
                    true,
                    new Progress<string>(chunk =>
                    {
                        ThreadHelper.JoinableTaskFactory.Run(async () =>
                        {
                            await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                            UpdateTypingMessage(chunk);
                        });
                    }),
                    cancellationToken).ConfigureAwait(false);

                // 移除正在输入指示器并添加完整响应
                RemoveTypingMessage();
                var assistantMessage = new ChatMessage
                {
                    Role = "assistant",
                    Content = response,
                    Timestamp = DateTime.Now
                };
                _chatHistory.Add(assistantMessage);
                AddMessageToUI(assistantMessage);

                await SaveChatHistoryAsync().ConfigureAwait(false);
            }
            catch (OperationCanceledException)
            {
                RemoveTypingMessage();
            }
            catch (Exception ex)
            {
                RemoveTypingMessage();

                // 详细的错误分析和日志记录
                string errorDetails = AnalyzeError(ex);
                ActivityLog.LogError("ChatToolWindowControl", $"LLM 请求失败: {ex.Message}");
                ActivityLog.LogError("ChatToolWindowControl", $"错误分析: {errorDetails}");

                var errorMessage = new ChatMessage
                {
                    Role = "assistant",
                    Content = $"错误: {ex.Message}\n\n{errorDetails}",
                    Timestamp = DateTime.Now,
                    IsError = true
                };
                AddMessageToUI(errorMessage);
            }
        }

        private void AddMessageToUI(ChatMessage message)
        {
            ThreadHelper.JoinableTaskFactory.Run(async () =>
            {
                await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();

                var messageControl = CreateMessageControl(message);
                ChatMessagesPanel.Children.Add(messageControl);

                // 滚动到底部
                ChatScrollViewer.ScrollToEnd();
            });
        }

        private FrameworkElement CreateMessageControl(ChatMessage message)
        {
            var mainContainer = new Grid();

            if (message.Role == "user")
            {
                // 用户消息：编辑按钮 + 消息内容
                mainContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(50) }); // 增加宽度以容纳编辑按钮
                mainContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

                // 编辑按钮（鼠标悬停时显示）
                var editButton = new Button
                {
                    Content = "编辑",
                    Width = 40,
                    Height = 24,
                    FontSize = 10,
                    Background = new SolidColorBrush(Color.FromArgb(200, 255, 255, 255)), // 半透明白色背景
                    BorderThickness = new Thickness(1),
                    BorderBrush = Brushes.LightGray,
                    Opacity = 0,
                    Margin = new Thickness(5, 5, 5, 0),
                    VerticalAlignment = VerticalAlignment.Top,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Cursor = Cursors.Hand,
                    ToolTip = "编辑并重新发送此消息"
                };
                editButton.Click += (s, e) => StartEditMessage(message);
                Grid.SetColumn(editButton, 0);

                // 消息边框
                var border = CreateMessageBorder(message);
                Grid.SetColumn(border, 1);

                // 鼠标悬停效果 - 改进的动画效果
                mainContainer.MouseEnter += (s, e) =>
                {
                    editButton.Opacity = 1;
                    editButton.Background = new SolidColorBrush(Color.FromArgb(255, 255, 255, 255)); // 完全不透明
                };
                mainContainer.MouseLeave += (s, e) =>
                {
                    if (!_isEditMode || _editingMessage != message)
                    {
                        editButton.Opacity = 0;
                    }
                };

                // 如果当前正在编辑这条消息，保持按钮可见
                if (_isEditMode && _editingMessage == message)
                {
                    editButton.Opacity = 1;
                    editButton.Background = new SolidColorBrush(Color.FromArgb(255, 173, 216, 230)); // 浅蓝色表示编辑状态
                }

                mainContainer.Children.Add(editButton);
                mainContainer.Children.Add(border);
                mainContainer.HorizontalAlignment = HorizontalAlignment.Right;
            }
            else
            {
                // AI 消息：只有消息内容
                mainContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
                var border = CreateMessageBorder(message);
                Grid.SetColumn(border, 0);
                mainContainer.Children.Add(border);
                mainContainer.HorizontalAlignment = HorizontalAlignment.Left;
            }

            return mainContainer;
        }

        private Border CreateMessageBorder(ChatMessage message)
        {
            var border = new Border
            {
                Margin = new Thickness(5),
                Padding = new Thickness(10),
                CornerRadius = new CornerRadius(5),
                Tag = message
            };

            // 设置自适应宽度，最大宽度为窗口宽度的70%
            border.SetBinding(FrameworkElement.MaxWidthProperty, new System.Windows.Data.Binding("ActualWidth")
            {
                Source = this,
                Converter = new WidthPercentageConverter(),
                ConverterParameter = 0.7
            });

            if (message.Role == "user")
            {
                border.Background = new SolidColorBrush(Color.FromRgb(128, 128, 128)); // 灰色背景
            }
            else
            {
                border.Background = new SolidColorBrush(Color.FromRgb(240, 240, 240));
                if (message.IsError)
                {
                    border.Background = new SolidColorBrush(Color.FromRgb(255, 200, 200));
                }
            }

            // 为用户消息创建包含发送按钮的容器
            if (message.Role == "user")
            {
                var mainGrid = new Grid();
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                var contentStackPanel = CreateMessageContent(message);
                Grid.SetRow(contentStackPanel, 0);
                mainGrid.Children.Add(contentStackPanel);

                // 编辑模式下的按钮容器（在消息内容区域的右下角）
                var buttonPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    HorizontalAlignment = HorizontalAlignment.Right,
                    Margin = new Thickness(0, 5, 0, 0),
                    Visibility = (_isEditMode && _editingMessage == message) ? Visibility.Visible : Visibility.Collapsed
                };

                // 取消编辑按钮
                var cancelEditButton = new Button
                {
                    Content = "取消",
                    Width = 50,
                    Height = 25,
                    Margin = new Thickness(0, 0, 5, 0),
                    Background = new SolidColorBrush(Color.FromRgb(128, 128, 128)),
                    Foreground = Brushes.White,
                    BorderThickness = new Thickness(0),
                    Tag = message
                };
                cancelEditButton.Click += CancelEditButton_Click;

                // 重新发送按钮
                var inlineEditSendButton = new Button
                {
                    Content = "发送",
                    Width = 50,
                    Height = 25,
                    Background = new SolidColorBrush(Color.FromRgb(0, 120, 212)),
                    Foreground = Brushes.White,
                    BorderThickness = new Thickness(0),
                    Tag = message
                };
                inlineEditSendButton.Click += InlineEditSendButton_Click;

                buttonPanel.Children.Add(cancelEditButton);
                buttonPanel.Children.Add(inlineEditSendButton);

                Grid.SetRow(buttonPanel, 1);
                mainGrid.Children.Add(buttonPanel);

                border.Child = mainGrid;
            }
            else
            {
                var stackPanel = CreateMessageContent(message);
                border.Child = stackPanel;
            }

            return border;
        }

        private StackPanel CreateMessageContent(ChatMessage message)
        {
            var stackPanel = new StackPanel();

            // 解析思考过程
            var (displayContent, thinkingContent) = ParseThinkingContent(message.Content);

            // 如果有思考过程，添加思考过程展开器
            if (!string.IsNullOrEmpty(thinkingContent))
            {
                var expander = new Expander
                {
                    Header = "💭 思考过程",
                    IsExpanded = false,
                    Margin = new Thickness(0, 0, 0, 5)
                };

                var thinkingTextBox = new TextBox
                {
                    Text = thinkingContent,
                    TextWrapping = TextWrapping.Wrap,
                    Foreground = Brushes.Gray,
                    FontStyle = FontStyles.Italic,
                    IsReadOnly = true,
                    BorderThickness = new Thickness(0),
                    Background = Brushes.Transparent,
                    Cursor = Cursors.IBeam
                };

                expander.Content = thinkingTextBox;
                stackPanel.Children.Add(expander);
            }

            // 主要内容 - 在编辑模式下可编辑
            var contentTextBox = new TextBox
            {
                Text = displayContent,
                TextWrapping = TextWrapping.Wrap,
                Foreground = message.Role == "user" ? Brushes.White : Brushes.Black,
                IsReadOnly = !(_isEditMode && _editingMessage == message && message.Role == "user"),
                BorderThickness = (_isEditMode && _editingMessage == message && message.Role == "user") ? new Thickness(1) : new Thickness(0),
                BorderBrush = (_isEditMode && _editingMessage == message && message.Role == "user") ? Brushes.White : Brushes.Transparent,
                Background = Brushes.Transparent,
                Cursor = (_isEditMode && _editingMessage == message && message.Role == "user") ? Cursors.IBeam : Cursors.Arrow,
                Tag = message
            };

            if (message.IsTyping)
            {
                contentTextBox.Tag = "typing";
            }

            // 添加右键菜单
            var contextMenu = new ContextMenu();

            var copyMenuItem = new MenuItem { Header = "复制消息" };
            copyMenuItem.Click += (s, e) => CopyMessageToClipboard(displayContent);
            contextMenu.Items.Add(copyMenuItem);

            if (!string.IsNullOrEmpty(thinkingContent))
            {
                var copyThinkingMenuItem = new MenuItem { Header = "复制思考过程" };
                copyThinkingMenuItem.Click += (s, e) => CopyMessageToClipboard(thinkingContent);
                contextMenu.Items.Add(copyThinkingMenuItem);
            }

            contentTextBox.ContextMenu = contextMenu;

            stackPanel.Children.Add(contentTextBox);

            // 时间戳
            var timeStamp = new TextBlock
            {
                Text = message.Timestamp.ToString("HH:mm:ss"),
                FontSize = 10,
                Opacity = 0.7,
                HorizontalAlignment = HorizontalAlignment.Right,
                Foreground = message.Role == "user" ? Brushes.White : Brushes.Gray
            };
            stackPanel.Children.Add(timeStamp);

            return stackPanel;
        }

        /// <summary>
        /// 解析思考过程内容
        /// </summary>
        /// <param name="content">原始内容</param>
        /// <returns>(显示内容, 思考内容)</returns>
        private (string displayContent, string thinkingContent) ParseThinkingContent(string content)
        {
            if (string.IsNullOrEmpty(content))
                return (content, string.Empty);

            var thinkingPattern = @"<think>(.*?)</think>";
            var matches = System.Text.RegularExpressions.Regex.Matches(content, thinkingPattern,
                System.Text.RegularExpressions.RegexOptions.Singleline);

            if (matches.Count == 0)
                return (content, string.Empty);

            var thinkingBuilder = new StringBuilder();
            var displayContent = content;

            foreach (System.Text.RegularExpressions.Match match in matches)
            {
                thinkingBuilder.AppendLine(match.Groups[1].Value.Trim());
                displayContent = displayContent.Replace(match.Value, "");
            }

            return (displayContent.Trim(), thinkingBuilder.ToString().Trim());
        }

        private void UpdateTypingMessage(string chunk)
        {
            // 找到正在输入的消息并更新内容
            foreach (FrameworkElement element in ChatMessagesPanel.Children)
            {
                TextBox typingTextBox = null;

                // 处理新的 Grid 结构
                if (element is Grid grid)
                {
                    foreach (var child in grid.Children)
                    {
                        if (child is Border border && border.Child is StackPanel stackPanel)
                        {
                            typingTextBox = stackPanel.Children.OfType<TextBox>()
                                .FirstOrDefault(tb => tb.Tag?.ToString() == "typing");
                            if (typingTextBox != null) break;
                        }
                    }
                }
                // 处理旧的 Border 结构（兼容性）
                else if (element is Border directBorder && directBorder.Child is StackPanel directStackPanel)
                {
                    typingTextBox = directStackPanel.Children.OfType<TextBox>()
                        .FirstOrDefault(tb => tb.Tag?.ToString() == "typing");
                }

                if (typingTextBox != null)
                {
                    if (typingTextBox.Text == Constants.UI.LoadingText)
                    {
                        typingTextBox.Text = chunk;
                    }
                    else
                    {
                        typingTextBox.Text += chunk;
                    }
                    break;
                }
            }

            // 滚动到底部
            ChatScrollViewer.ScrollToEnd();
        }

        private void RemoveTypingMessage()
        {
            ThreadHelper.JoinableTaskFactory.Run(async () =>
            {
                await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();

                // 移除正在输入的消息
                var toRemove = new List<FrameworkElement>();

                foreach (FrameworkElement element in ChatMessagesPanel.Children)
                {
                    bool hasTypingMessage = false;

                    // 处理新的 Grid 结构
                    if (element is Grid grid)
                    {
                        foreach (var child in grid.Children)
                        {
                            if (child is Border border && border.Child is StackPanel stackPanel)
                            {
                                var textBox = stackPanel.Children.OfType<TextBox>()
                                    .FirstOrDefault(tb => tb.Tag?.ToString() == "typing");
                                if (textBox != null)
                                {
                                    hasTypingMessage = true;
                                    break;
                                }
                            }
                        }
                    }
                    // 处理旧的 Border 结构（兼容性）
                    else if (element is Border directBorder && directBorder.Child is StackPanel directStackPanel)
                    {
                        var textBox = directStackPanel.Children.OfType<TextBox>()
                            .FirstOrDefault(tb => tb.Tag?.ToString() == "typing");
                        if (textBox != null)
                        {
                            hasTypingMessage = true;
                        }
                    }

                    if (hasTypingMessage)
                    {
                        toRemove.Add(element);
                    }
                }

                foreach (var element in toRemove)
                {
                    ChatMessagesPanel.Children.Remove(element);
                }
            });
        }

        private void AddSystemMessage(string message)
        {
            var systemMessage = new ChatMessage
            {
                Role = "system",
                Content = message,
                Timestamp = DateTime.Now
            };
            AddMessageToUI(systemMessage);
        }

        private void ProviderComboBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            _ = UpdateProviderAsync();
        }

        private void ClearChat_Click(object sender, RoutedEventArgs e)
        {
            _ = ClearChatAsync();
        }

        private async Task ClearChatAsync()
        {
            await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();

            _chatHistory.Clear();
            ChatMessagesPanel.Children.Clear();

            // 重置 Token 统计
            _currentHistoryTokens = 0;
            _currentContextTokens = 0;
            ContextWarningPanel.Visibility = Visibility.Collapsed;

            await SaveChatHistoryAsync().ConfigureAwait(false);
            await UpdateTokenStatsAsync().ConfigureAwait(false);
        }

        private void ExportChat_Click(object sender, RoutedEventArgs e)
        {
            _ = ExportChatAsync();
        }

        private async Task ExportChatAsync()
        {
            try
            {
                var markdown = new StringBuilder();
                markdown.AppendLine("# AI Code Assistant 聊天记录");
                markdown.AppendLine($"导出时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                markdown.AppendLine();

                foreach (var message in _chatHistory)
                {
                    markdown.AppendLine($"## {(message.Role == "user" ? "用户" : "助手")} ({message.Timestamp:HH:mm:ss})");
                    markdown.AppendLine();
                    markdown.AppendLine(message.Content);
                    markdown.AppendLine();
                }

                var fileName = $"chat_export_{DateTime.Now:yyyyMMdd_HHmmss}.md";
                var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                var filePath = Path.Combine(desktopPath, fileName);

                File.WriteAllText(filePath, markdown.ToString());

                await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                await VS.MessageBox.ShowAsync("导出成功", $"聊天记录已导出到桌面: {fileName}").ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("ChatToolWindowControl", $"导出聊天记录失败: {ex.Message}");
                await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                await VS.MessageBox.ShowErrorAsync("导出失败", ex.Message).ConfigureAwait(false);
            }
        }

        private async Task LoadChatHistoryAsync()
        {
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var folderPath = Path.Combine(appDataPath, Constants.Config.AppDataFolder);
                var filePath = Path.Combine(folderPath, Constants.Config.ChatLogFileName);

                if (File.Exists(filePath))
                {
                    var json = File.ReadAllText(filePath);
                    var messages = JsonSerializer.Deserialize<List<ChatMessage>>(json);

                    if (messages != null)
                    {
                        _chatHistory.Clear();
                        _chatHistory.AddRange(messages); // 不再限制历史记录数量，由 Token 限制控制

                        await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                        foreach (var message in _chatHistory)
                        {
                            AddMessageToUI(message);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("ChatToolWindowControl", $"加载聊天历史失败: {ex.Message}");
            }
        }

        private Task SaveChatHistoryAsync()
        {
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var folderPath = Path.Combine(appDataPath, Constants.Config.AppDataFolder);

                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }

                var filePath = Path.Combine(folderPath, Constants.Config.ChatLogFileName);
                var json = JsonSerializer.Serialize(_chatHistory, new JsonSerializerOptions { WriteIndented = true });

                File.WriteAllText(filePath, json);
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("ChatToolWindowControl", $"保存聊天历史失败: {ex.Message}");
                return Task.CompletedTask;
            }
        }

        /// <summary>
        /// 分析错误原因并提供解决建议
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <returns>错误分析结果</returns>
        private string AnalyzeError(Exception ex)
        {
            var analysis = new StringBuilder();

            // 检查是否是 HTTP 相关错误
            if (ex.Message.Contains("404") || ex.Message.Contains("Not Found"))
            {
                analysis.AppendLine("🔍 404 错误分析:");
                analysis.AppendLine("• 可能原因：Ollama API 端点不正确或模型不存在");
                analysis.AppendLine("• 建议检查：");
                analysis.AppendLine("  1. Ollama 服务是否正在运行 (ollama serve)");
                analysis.AppendLine("  2. API 地址是否正确 (默认: http://localhost:11434)");
                analysis.AppendLine("  3. 选择的模型是否已安装 (ollama list)");
                analysis.AppendLine("  4. 在设置页面点击'测试连接'验证配置");
            }
            else if (ex.Message.Contains("连接") || ex.Message.Contains("Connection") || ex.Message.Contains("timeout"))
            {
                analysis.AppendLine("🔍 连接错误分析:");
                analysis.AppendLine("• 可能原因：无法连接到 Ollama 服务");
                analysis.AppendLine("• 建议检查：");
                analysis.AppendLine("  1. Ollama 是否已启动 (ollama serve)");
                analysis.AppendLine("  2. 防火墙是否阻止了连接");
                analysis.AppendLine("  3. API 地址和端口是否正确");
            }
            else if (ex.Message.Contains("模型") || ex.Message.Contains("model"))
            {
                analysis.AppendLine("🔍 模型错误分析:");
                analysis.AppendLine("• 可能原因：指定的模型不可用");
                analysis.AppendLine("• 建议检查：");
                analysis.AppendLine("  1. 模型是否已下载 (ollama pull <model_name>)");
                analysis.AppendLine("  2. 模型名称是否正确");
                analysis.AppendLine("  3. 在设置页面刷新模型列表");
            }
            else
            {
                analysis.AppendLine("🔍 通用错误分析:");
                analysis.AppendLine("• 建议操作：");
                analysis.AppendLine("  1. 检查 Ollama 服务状态");
                analysis.AppendLine("  2. 验证网络连接");
                analysis.AppendLine("  3. 查看设置页面的连接测试结果");
                analysis.AppendLine("  4. 检查 Visual Studio 输出窗口的详细日志");
            }

            return analysis.ToString();
        }

        /// <summary>
        /// 复制消息内容到剪贴板
        /// </summary>
        /// <param name="content">要复制的内容</param>
        private void CopyMessageToClipboard(string content)
        {
            try
            {
                Clipboard.SetText(content);
                ActivityLog.LogInformation("ChatToolWindowControl", "消息已复制到剪贴板");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("ChatToolWindowControl", $"复制到剪贴板失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 开始编辑消息
        /// </summary>
        /// <param name="message">要编辑的消息</param>
        private void StartEditMessage(ChatMessage message)
        {
            try
            {
                // 如果已经在编辑模式，先退出
                if (_isEditMode)
                {
                    ExitEditMode();
                }

                _isEditMode = true;
                _editingMessage = message;

                // 刷新UI以更新编辑状态和显示内联编辑控件
                RefreshMessageUI();

                ActivityLog.LogInformation("ChatToolWindowControl", $"进入编辑模式，编辑消息: {message.Content.Substring(0, Math.Min(50, message.Content.Length))}...");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("ChatToolWindowControl", $"开始编辑消息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 退出编辑模式
        /// </summary>
        private void ExitEditMode()
        {
            _isEditMode = false;
            _editingMessage = null;

            // 刷新UI以更新编辑状态
            RefreshMessageUI();
        }

        /// <summary>
        /// 刷新消息UI以更新编辑图标状态
        /// </summary>
        private void RefreshMessageUI()
        {
            ThreadHelper.JoinableTaskFactory.Run(async () =>
            {
                await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();

                // 重新创建所有消息控件以更新编辑图标状态
                var messages = _chatHistory.ToList();
                ChatMessagesPanel.Children.Clear();

                foreach (var msg in messages)
                {
                    if (!msg.IsTyping) // 不重新创建正在输入的消息
                    {
                        AddMessageToUI(msg);
                    }
                }
            });
        }

        /// <summary>
        /// 重新发送内联编辑后的消息
        /// </summary>
        private async Task ResendInlineEditedMessageAsync(Button sendButton)
        {
            if (!_isEditMode || _editingMessage == null || sendButton?.Tag is not ChatMessage message)
                return;

            try
            {
                // 找到编辑的文本框并获取新内容
                var newContent = GetEditedMessageContent(message);
                if (string.IsNullOrEmpty(newContent))
                    return;

                // 退出编辑模式
                ExitEditMode();

                await ProcessResendMessageAsync(message, newContent).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                ExitEditMode();
                RemoveTypingMessage();

                ActivityLog.LogError("ChatToolWindowControl", $"重新发送内联编辑消息失败: {ex.Message}");

                var errorMessage = new ChatMessage
                {
                    Role = "assistant",
                    Content = $"错误: {ex.Message}",
                    Timestamp = DateTime.Now,
                    IsError = true
                };
                AddMessageToUI(errorMessage);
            }
        }

        /// <summary>
        /// 重新发送编辑后的消息（输入框模式）
        /// </summary>
        private async Task ResendEditedMessageAsync()
        {
            if (!_isEditMode || _editingMessage == null)
                return;

            try
            {
                var newContent = InputTextBox.Text?.Trim();
                if (string.IsNullOrEmpty(newContent) || _isPlaceholderActive)
                    return;

                // 退出编辑模式
                ExitEditMode();

                await ProcessResendMessageAsync(_editingMessage, newContent).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                ExitEditMode();
                RemoveTypingMessage();
                ResetInputBoxToPlaceholder();

                ActivityLog.LogError("ChatToolWindowControl", $"重新发送编辑消息失败: {ex.Message}");

                var errorMessage = new ChatMessage
                {
                    Role = "assistant",
                    Content = $"错误: {ex.Message}",
                    Timestamp = DateTime.Now,
                    IsError = true
                };
                AddMessageToUI(errorMessage);
            }
        }

        /// <summary>
        /// 获取编辑后的消息内容
        /// </summary>
        private string GetEditedMessageContent(ChatMessage message)
        {
            try
            {
                // 在聊天面板中找到对应的文本框
                foreach (FrameworkElement element in ChatMessagesPanel.Children)
                {
                    if (element is Grid grid)
                    {
                        foreach (var child in grid.Children)
                        {
                            if (child is Border border && border.Tag == message && border.Child is Grid messageGrid)
                            {
                                // 在消息网格中查找内容 StackPanel
                                foreach (var gridChild in messageGrid.Children)
                                {
                                    if (gridChild is StackPanel stackPanel)
                                    {
                                        // 查找主要内容文本框
                                        foreach (var stackChild in stackPanel.Children)
                                        {
                                            if (stackChild is TextBox textBox && textBox.Tag == message && !textBox.IsReadOnly)
                                            {
                                                return textBox.Text?.Trim();
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("ChatToolWindowControl", $"获取编辑内容失败: {ex.Message}");
            }

            return string.Empty;
        }

        /// <summary>
        /// 处理重新发送消息的通用逻辑
        /// </summary>
        private async Task ProcessResendMessageAsync(ChatMessage originalMessage, string newContent)
        {
            try
            {
                // 找到要编辑的消息在历史中的位置
                var messageIndex = _chatHistory.IndexOf(originalMessage);
                if (messageIndex >= 0)
                {
                    // 移除从这条消息开始的所有后续消息
                    var messagesToRemove = _chatHistory.Skip(messageIndex).ToList();
                    foreach (var msg in messagesToRemove)
                    {
                        _chatHistory.Remove(msg);
                    }

                    // 更新UI - 移除对应的UI元素
                    await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                    var elementsToRemove = new List<FrameworkElement>();
                    foreach (FrameworkElement element in ChatMessagesPanel.Children)
                    {
                        bool shouldRemove = false;

                        // 检查 Grid 容器中的 Border 元素
                        if (element is Grid grid)
                        {
                            foreach (var child in grid.Children)
                            {
                                if (child is Border border && border.Tag is ChatMessage msg && messagesToRemove.Contains(msg))
                                {
                                    shouldRemove = true;
                                    break;
                                }
                            }
                        }
                        // 检查直接的 Border 元素（兼容旧版本）
                        else if (element is Border directBorder && directBorder.Tag is ChatMessage directMsg && messagesToRemove.Contains(directMsg))
                        {
                            shouldRemove = true;
                        }

                        if (shouldRemove)
                        {
                            elementsToRemove.Add(element);
                        }
                    }

                    foreach (var element in elementsToRemove)
                    {
                        ChatMessagesPanel.Children.Remove(element);
                    }

                    ActivityLog.LogInformation("ChatToolWindowControl", $"已移除 {messagesToRemove.Count} 条消息，准备重新发送");
                }

                // 清空编辑状态
                var wasEditingMessage = _editingMessage;
                _editingMessage = null;

                // 取消之前的请求
                _currentCancellation?.Cancel();
                _currentCancellation = new CancellationTokenSource();

                // 添加新的用户消息到聊天历史
                var userChatMessage = new ChatMessage
                {
                    Role = "user",
                    Content = newContent,
                    Timestamp = DateTime.Now
                };
                _chatHistory.Add(userChatMessage);
                AddMessageToUI(userChatMessage);

                // 显示正在输入指示器
                var typingMessage = new ChatMessage
                {
                    Role = "assistant",
                    Content = Constants.UI.LoadingText,
                    Timestamp = DateTime.Now,
                    IsTyping = true
                };
                AddMessageToUI(typingMessage);

                // 构建包含上下文的提示词（包含编辑前的上下文，使用 messages 格式）
                var prompt = await BuildChatMessagesWithContextAsync(newContent).ConfigureAwait(false);
                ActivityLog.LogInformation("ChatToolWindowControl", $"重新发送 - 构建的提示词长度: {prompt.Length} 字符");

                // 检查提供者是否可用
                if (_currentProvider == null)
                {
                    ActivityLog.LogError("ChatToolWindowControl", "当前提供者为空，重新初始化");
                    await UpdateProviderAsync().ConfigureAwait(false);

                    if (_currentProvider == null)
                    {
                        throw new InvalidOperationException("无法初始化 LLM 提供者");
                    }
                }

                // 发送到 LLM
                var response = await _currentProvider.SendAsync(
                    prompt,
                    true,
                    new Progress<string>(chunk =>
                    {
                        ThreadHelper.JoinableTaskFactory.Run(async () =>
                        {
                            await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                            UpdateTypingMessage(chunk);
                        });
                    }),
                    _currentCancellation.Token).ConfigureAwait(false);

                // 移除正在输入指示器并添加完整响应
                RemoveTypingMessage();
                var assistantMessage = new ChatMessage
                {
                    Role = "assistant",
                    Content = response,
                    Timestamp = DateTime.Now
                };
                _chatHistory.Add(assistantMessage);
                AddMessageToUI(assistantMessage);

                // 保存聊天历史
                await SaveChatHistoryAsync().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                ExitEditMode();
                RemoveTypingMessage();
                ResetInputBoxToPlaceholder();

                // 详细的错误分析和日志记录
                string errorDetails = AnalyzeError(ex);
                ActivityLog.LogError("ChatToolWindowControl", $"重新发送消息失败: {ex.Message}");
                ActivityLog.LogError("ChatToolWindowControl", $"错误分析: {errorDetails}");

                var errorMessage = new ChatMessage
                {
                    Role = "assistant",
                    Content = $"错误: {ex.Message}\n\n{errorDetails}",
                    Timestamp = DateTime.Now,
                    IsError = true
                };
                AddMessageToUI(errorMessage);
            }
        }

        /// <summary>
        /// 编辑并重新发送消息（保留旧方法兼容性）
        /// </summary>
        /// <param name="message">要编辑的消息</param>
        private void EditAndResendMessage(ChatMessage message)
        {
            StartEditMessage(message);
        }

        /// <summary>
        /// 重置输入框到占位符状态
        /// </summary>
        private void ResetInputBoxToPlaceholder()
        {
            ThreadHelper.JoinableTaskFactory.Run(async () =>
            {
                await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                InputTextBox.Text = PlaceholderText;
                InputTextBox.Foreground = Brushes.Gray;
                _isPlaceholderActive = true;
            });
        }
    }

    /// <summary>
    /// 宽度百分比转换器
    /// </summary>
    public class WidthPercentageConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double width && parameter is double percentage)
            {
                var maxWidth = width * percentage;
                return Math.Max(200, maxWidth); // 最小宽度200
            }
            return 350; // 默认宽度
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}