using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.Text;
using Microsoft.VisualStudio.Shell;
using Microsoft.VisualStudio.Text;
using Microsoft.VisualStudio.Text.Editor;

namespace AICodeAssistant.Services
{
    /// <summary>
    /// 代码上下文提取器，用于提取光标周围的代码上下文
    /// </summary>
    public class ContextExtractor
    {
        private const int DefaultContextLines = 20;
        private const int MaxContextTokens = 2000;

        /// <summary>
        /// 提取代码上下文信息
        /// </summary>
        /// <param name="textView">文本视图</param>
        /// <param name="contextLines">上下文行数</param>
        /// <returns>上下文信息</returns>
        public static async Task<CodeContext> ExtractContextAsync(ITextView textView, int contextLines = DefaultContextLines)
        {
            if (textView?.TextBuffer?.CurrentSnapshot == null)
                return new CodeContext();

            try
            {
                var snapshot = textView.TextBuffer.CurrentSnapshot;
                var caretPosition = textView.Caret.Position.BufferPosition;
                var caretLine = snapshot.GetLineFromPosition(caretPosition);

                // 获取文件路径
                var filePath = GetFilePath(textView);
                var language = GetLanguageFromFilePath(filePath);

                // 提取基本上下文
                var context = ExtractBasicContext(snapshot, caretLine, contextLines, caretPosition.Position);

                // 如果是 C# 文件，尝试使用 Roslyn 进行语法分析
                if (language == "csharp")
                {
                    var syntaxContext = await ExtractSyntaxContextAsync(snapshot.GetText(), caretPosition.Position).ConfigureAwait(false);
                    if (syntaxContext != null)
                    {
                        context.SyntaxContext = syntaxContext;
                    }
                }

                context.FilePath = filePath;
                context.Language = language;
                context.CaretPosition = caretPosition.Position;
                context.CaretLine = caretLine.LineNumber + 1; // 1-based line number

                return context;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("ContextExtractor", $"提取上下文失败: {ex.Message}");
                return new CodeContext();
            }
        }

        private static CodeContext ExtractBasicContext(ITextSnapshot snapshot, ITextSnapshotLine caretLine, int contextLines, int caretPosition)
        {
            var context = new CodeContext();
            
            // 计算上下文范围
            var startLine = Math.Max(0, caretLine.LineNumber - contextLines);
            var endLine = Math.Min(snapshot.LineCount - 1, caretLine.LineNumber + contextLines);

            var beforeCaret = new StringBuilder();
            var afterCaret = new StringBuilder();

            // 提取光标前的代码
            for (int i = startLine; i <= caretLine.LineNumber; i++)
            {
                var line = snapshot.GetLineFromLineNumber(i);
                var lineText = line.GetText();
                
                if (i == caretLine.LineNumber)
                {
                    // 当前行只取光标前的部分
                    var caretColumn = caretPosition - line.Start.Position;
                    if (caretColumn > 0 && caretColumn <= lineText.Length)
                    {
                        beforeCaret.AppendLine(lineText.Substring(0, caretColumn));
                    }
                }
                else
                {
                    beforeCaret.AppendLine(lineText);
                }
            }

            // 提取光标后的代码
            for (int i = caretLine.LineNumber; i <= endLine; i++)
            {
                var line = snapshot.GetLineFromLineNumber(i);
                var lineText = line.GetText();
                
                if (i == caretLine.LineNumber)
                {
                    // 当前行只取光标后的部分
                    var caretColumn = caretPosition - line.Start.Position;
                    if (caretColumn < lineText.Length)
                    {
                        afterCaret.AppendLine(lineText.Substring(caretColumn));
                    }
                }
                else
                {
                    afterCaret.AppendLine(lineText);
                }
            }

            context.BeforeCaret = beforeCaret.ToString().TrimEnd();
            context.AfterCaret = afterCaret.ToString().TrimStart();
            
            return context;
        }

        private static async Task<SyntaxContext> ExtractSyntaxContextAsync(string sourceCode, int caretPosition)
        {
            try
            {
                var syntaxTree = CSharpSyntaxTree.ParseText(sourceCode);
                var root = await syntaxTree.GetRootAsync().ConfigureAwait(false);
                
                // 找到光标位置的语法节点
                var node = root.FindNode(new TextSpan(caretPosition, 0));
                
                var syntaxContext = new SyntaxContext
                {
                    CurrentNode = node?.GetType().Name ?? "Unknown",
                    CurrentNodeText = node?.ToString() ?? string.Empty
                };

                // 获取包含的方法或属性
                var containingMember = node?.Ancestors().FirstOrDefault(n => 
                    n is Microsoft.CodeAnalysis.CSharp.Syntax.MethodDeclarationSyntax ||
                    n is Microsoft.CodeAnalysis.CSharp.Syntax.PropertyDeclarationSyntax ||
                    n is Microsoft.CodeAnalysis.CSharp.Syntax.ConstructorDeclarationSyntax);

                if (containingMember != null)
                {
                    syntaxContext.ContainingMember = containingMember.ToString();
                }

                // 获取包含的类
                var containingClass = node?.Ancestors().FirstOrDefault(n => 
                    n is Microsoft.CodeAnalysis.CSharp.Syntax.ClassDeclarationSyntax ||
                    n is Microsoft.CodeAnalysis.CSharp.Syntax.InterfaceDeclarationSyntax ||
                    n is Microsoft.CodeAnalysis.CSharp.Syntax.StructDeclarationSyntax);

                if (containingClass != null)
                {
                    syntaxContext.ContainingType = containingClass.ToString();
                }

                return syntaxContext;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("ContextExtractor", $"语法分析失败: {ex.Message}");
                return null;
            }
        }

        private static string GetFilePath(ITextView textView)
        {
            try
            {
                if (textView.TextBuffer.Properties.TryGetProperty(typeof(ITextDocument), out ITextDocument document))
                {
                    return document.FilePath;
                }
            }
            catch
            {
                // 忽略异常
            }
            
            return "Unknown";
        }

        private static string GetLanguageFromFilePath(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return "text";

            var extension = System.IO.Path.GetExtension(filePath).ToLowerInvariant();
            return extension switch
            {
                ".cs" => "csharp",
                ".vb" => "vb",
                ".cpp" or ".cc" or ".cxx" => "cpp",
                ".c" => "c",
                ".h" or ".hpp" => "c",
                ".js" => "javascript",
                ".ts" => "typescript",
                ".py" => "python",
                ".java" => "java",
                ".xml" or ".xaml" => "xml",
                ".json" => "json",
                ".html" or ".htm" => "html",
                ".css" => "css",
                ".sql" => "sql",
                _ => "text"
            };
        }
    }

    /// <summary>
    /// 代码上下文信息
    /// </summary>
    public class CodeContext
    {
        public string FilePath { get; set; } = string.Empty;
        public string Language { get; set; } = "text";
        public int CaretPosition { get; set; }
        public int CaretLine { get; set; }
        public string BeforeCaret { get; set; } = string.Empty;
        public string AfterCaret { get; set; } = string.Empty;
        public SyntaxContext SyntaxContext { get; set; }
    }

    /// <summary>
    /// 语法上下文信息
    /// </summary>
    public class SyntaxContext
    {
        public string CurrentNode { get; set; } = string.Empty;
        public string CurrentNodeText { get; set; } = string.Empty;
        public string ContainingMember { get; set; } = string.Empty;
        public string ContainingType { get; set; } = string.Empty;
    }
}
