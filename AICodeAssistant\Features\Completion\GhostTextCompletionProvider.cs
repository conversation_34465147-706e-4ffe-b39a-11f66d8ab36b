using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.VisualStudio.Text;
using Microsoft.VisualStudio.Text.Editor;
using Microsoft.VisualStudio.Utilities;
using Microsoft.VisualStudio.Shell;
using AICodeAssistant.Services;
using AICodeAssistant.UI;
using AICodeAssistant.Resources;

namespace AICodeAssistant.Features.Completion
{
    /// <summary>
    /// Ghost Text AI代码补全提供者
    /// 使用Visual Studio 2022的Ghost Text API实现标准的AI代码补全
    /// </summary>
    [Export(typeof(IGhostTextProvider))]
    [Name("AI Ghost Text Provider")]
    [ContentType("code")]
    [ContentType("text")]
    [TextViewRole(PredefinedTextViewRoles.Editable)]
    public class GhostTextCompletionProvider : IGhostTextProvider
    {
        private readonly Dictionary<ITextView, GhostTextSession> _sessions = new Dictionary<ITextView, GhostTextSession>();
        private readonly object _lockObject = new object();

        public async Task<IGhostTextSuggestion> GetGhostTextAsync(
            ITextView textView,
            SnapshotPoint position,
            CancellationToken cancellationToken)
        {
            try
            {
                ActivityLog.LogInformation("GhostTextCompletionProvider", 
                    $"Ghost Text请求 - 位置: {position.Position}");

                // 检查是否应该提供Ghost Text
                if (!ShouldProvideGhostText(textView, position))
                {
                    return null;
                }

                // 获取或创建会话
                var session = GetOrCreateSession(textView);
                if (session == null)
                {
                    return null;
                }

                // 生成Ghost Text建议
                var suggestion = await session.GetSuggestionAsync(position, cancellationToken);
                if (suggestion != null)
                {
                    ActivityLog.LogInformation("GhostTextCompletionProvider", 
                        $"生成Ghost Text: '{suggestion.Text}'");
                }

                return suggestion;
            }
            catch (OperationCanceledException)
            {
                ActivityLog.LogInformation("GhostTextCompletionProvider", "Ghost Text请求被取消");
                return null;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextCompletionProvider", $"Ghost Text生成失败: {ex.Message}");
                return null;
            }
        }

        public void Dispose()
        {
            lock (_lockObject)
            {
                foreach (var session in _sessions.Values)
                {
                    session?.Dispose();
                }
                _sessions.Clear();
            }
        }

        /// <summary>
        /// 判断是否应该提供Ghost Text
        /// </summary>
        private bool ShouldProvideGhostText(ITextView textView, SnapshotPoint position)
        {
            try
            {
                // 获取当前行
                var line = position.GetContainingLine();
                var lineText = line.GetText();
                var positionInLine = position.Position - line.Start.Position;

                // 检查是否在行尾或接近行尾
                var trimmedLine = lineText.TrimEnd();
                if (positionInLine < trimmedLine.Length - 1)
                {
                    return false;
                }

                // 检查最小行长度
                if (trimmedLine.Length < 3)
                {
                    return false;
                }

                // 检查文件类型
                var filePath = GetFilePath(textView);
                if (!IsSupportedFileType(filePath))
                {
                    return false;
                }

                // 检查是否在注释或字符串中
                if (IsInCommentOrString(position))
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextCompletionProvider", $"判断Ghost Text条件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取或创建Ghost Text会话
        /// </summary>
        private GhostTextSession GetOrCreateSession(ITextView textView)
        {
            try
            {
                lock (_lockObject)
                {
                    if (_sessions.TryGetValue(textView, out var existingSession))
                    {
                        return existingSession;
                    }

                    // 加载设置
                    var settings = LoadSettings();
                    if (settings?.Completion?.Enabled != true)
                    {
                        return null;
                    }

                    // 获取LLM提供者
                    var llmProvider = GetLlmProvider(settings);
                    if (llmProvider == null || !llmProvider.IsAvailable)
                    {
                        return null;
                    }

                    // 创建新会话
                    var session = new GhostTextSession(textView, llmProvider, settings.Completion);
                    _sessions[textView] = session;

                    // 监听文本视图关闭事件
                    textView.Closed += (sender, e) =>
                    {
                        lock (_lockObject)
                        {
                            if (_sessions.TryGetValue(textView, out var sessionToRemove))
                            {
                                sessionToRemove.Dispose();
                                _sessions.Remove(textView);
                            }
                        }
                    };

                    ActivityLog.LogInformation("GhostTextCompletionProvider", "创建新的Ghost Text会话");
                    return session;
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextCompletionProvider", $"创建Ghost Text会话失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 加载设置
        /// </summary>
        private AppSettings LoadSettings()
        {
            try
            {
                return SettingsManager.LoadSettings() ?? AppSettings.CreateDefault();
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextCompletionProvider", $"加载设置失败: {ex.Message}");
                return AppSettings.CreateDefault();
            }
        }

        /// <summary>
        /// 获取LLM提供者
        /// </summary>
        private ILlmProvider GetLlmProvider(AppSettings settings)
        {
            try
            {
                var options = CreateLlmOptions(settings);
                return LlmProviderFactory.CreateProvider(settings.ProviderType, options);
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextCompletionProvider", $"创建LLM提供者失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建LLM选项
        /// </summary>
        private LlmOptions CreateLlmOptions(AppSettings settings)
        {
            return settings.ProviderType switch
            {
                LlmProviderType.OpenAI => new LlmOptions
                {
                    ApiKey = settings.OpenAI.ApiKey,
                    ApiBase = settings.OpenAI.ApiBase,
                    Model = settings.OpenAI.Model,
                    Temperature = 0.1, // 更低的温度获得更确定的结果
                    MaxTokens = 128,   // Ghost Text通常较短
                    TimeoutSeconds = 8
                },
                LlmProviderType.Ollama => new LlmOptions
                {
                    ApiBase = settings.Ollama.ApiBase,
                    Model = settings.Ollama.Model,
                    Temperature = 0.1,
                    MaxTokens = 128,
                    TimeoutSeconds = 12
                },
                _ => LlmProviderFactory.GetDefaultOptions(LlmProviderType.Ollama)
            };
        }

        /// <summary>
        /// 获取文件路径
        /// </summary>
        private string GetFilePath(ITextView textView)
        {
            try
            {
                if (textView.TextBuffer.Properties.TryGetProperty(typeof(ITextDocument), out ITextDocument document))
                {
                    return document.FilePath ?? "Unknown";
                }
            }
            catch { }
            return "Unknown";
        }

        /// <summary>
        /// 检查文件类型是否支持
        /// </summary>
        private bool IsSupportedFileType(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || filePath == "Unknown")
                return true;

            var extension = System.IO.Path.GetExtension(filePath)?.ToLowerInvariant();
            return Constants.FileExtensions.SupportedCodeFiles.Contains(extension);
        }

        /// <summary>
        /// 检查是否在注释或字符串中
        /// </summary>
        private bool IsInCommentOrString(SnapshotPoint position)
        {
            try
            {
                var line = position.GetContainingLine();
                var lineText = line.GetText();
                var pos = position.Position - line.Start.Position;

                if (pos > 0 && pos < lineText.Length)
                {
                    var beforeCursor = lineText.Substring(0, pos);
                    
                    // 检查是否在字符串中
                    var quoteCount = beforeCursor.Count(c => c == '"');
                    if (quoteCount % 2 == 1) return true;

                    // 检查是否在注释中
                    if (beforeCursor.TrimStart().StartsWith("//")) return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// Ghost Text提供者接口
    /// </summary>
    public interface IGhostTextProvider : IDisposable
    {
        Task<IGhostTextSuggestion> GetGhostTextAsync(
            ITextView textView,
            SnapshotPoint position,
            CancellationToken cancellationToken);
    }

    /// <summary>
    /// Ghost Text建议接口
    /// </summary>
    public interface IGhostTextSuggestion
    {
        string Text { get; }
        SnapshotSpan ApplicableSpan { get; }
        string Description { get; }
    }

    /// <summary>
    /// Ghost Text建议实现
    /// </summary>
    public class GhostTextSuggestion : IGhostTextSuggestion
    {
        public string Text { get; }
        public SnapshotSpan ApplicableSpan { get; }
        public string Description { get; }

        public GhostTextSuggestion(string text, SnapshotSpan applicableSpan, string description = null)
        {
            Text = text ?? throw new ArgumentNullException(nameof(text));
            ApplicableSpan = applicableSpan;
            Description = description ?? $"AI建议: {text}";
        }
    }
}
