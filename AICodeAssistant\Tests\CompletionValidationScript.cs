using System;
using System.Threading.Tasks;
using AICodeAssistant.Tests;
using AICodeAssistant.Services;
using AICodeAssistant.UI;

namespace AICodeAssistant.Tests
{
    /// <summary>
    /// 代码补全功能验证脚本
    /// </summary>
    public class CompletionValidationScript
    {
        /// <summary>
        /// 运行完整的验证测试
        /// </summary>
        public static async Task RunValidationAsync()
        {
            Console.WriteLine("=== AI 代码补全功能验证 ===\n");

            try
            {
                // 1. 测试基础配置
                await TestBasicConfigurationAsync();
                Console.WriteLine();

                // 2. 测试上下文提取
                await TestContextExtractionAsync();
                Console.WriteLine();

                // 3. 测试 LLM 提供者
                await TestLlmProviderAsync();
                Console.WriteLine();

                // 4. 运行单元测试
                RunUnitTests();
                Console.WriteLine();

                Console.WriteLine("✅ 所有验证测试通过！");
                Console.WriteLine("🚀 代码补全功能已准备就绪");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 验证过程中出现错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex.StackTrace}");
            }

            Console.WriteLine("\n=== 验证完成 ===");
        }

        /// <summary>
        /// 测试基础配置
        /// </summary>
        private static async Task TestBasicConfigurationAsync()
        {
            Console.WriteLine("📋 测试基础配置...");

            try
            {
                // 测试默认设置
                var defaultSettings = AppSettings.CreateDefault();
                Console.WriteLine($"✓ 默认设置创建成功");
                Console.WriteLine($"  - 补全功能启用: {defaultSettings.Completion.Enabled}");
                Console.WriteLine($"  - 上下文行数: {defaultSettings.Completion.ContextLines}");
                Console.WriteLine($"  - 最大 Token 数: {defaultSettings.Completion.MaxTokens}");
                Console.WriteLine($"  - 温度参数: {defaultSettings.Completion.Temperature}");

                // 测试常量定义
                Console.WriteLine($"✓ 常量定义验证");
                Console.WriteLine($"  - 触发字符数量: {Constants.Completion.TriggerCharacters.Length}");
                Console.WriteLine($"  - 最大补全项: {Constants.Completion.MaxCompletionItems}");
                Console.WriteLine($"  - 最小触发长度: {Constants.Completion.MinTriggerLength}");

                await Task.Delay(100); // 模拟异步操作
                Console.WriteLine("✅ 基础配置测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 基础配置测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 测试上下文提取
        /// </summary>
        private static async Task TestContextExtractionAsync()
        {
            Console.WriteLine("🔍 测试上下文提取...");

            try
            {
                // 模拟代码上下文
                var mockContext = new CodeContext
                {
                    FilePath = "TestFile.cs",
                    Language = "csharp",
                    BeforeCaret = "public class TestClass\n{\n    public void Method()\n    {\n        var result = ",
                    AfterCaret = ";\n        Console.WriteLine(result);\n    }\n}",
                    CaretLine = 5,
                    CaretPosition = 85
                };

                Console.WriteLine($"✓ 模拟上下文创建成功");
                Console.WriteLine($"  - 文件路径: {mockContext.FilePath}");
                Console.WriteLine($"  - 编程语言: {mockContext.Language}");
                Console.WriteLine($"  - 光标位置: 第 {mockContext.CaretLine} 行，位置 {mockContext.CaretPosition}");
                Console.WriteLine($"  - 光标前代码长度: {mockContext.BeforeCaret?.Length ?? 0} 字符");
                Console.WriteLine($"  - 光标后代码长度: {mockContext.AfterCaret?.Length ?? 0} 字符");

                // 测试语法上下文
                var syntaxContext = new SyntaxContext
                {
                    CurrentNode = "LocalDeclarationStatement",
                    ContainingMember = "Method",
                    ContainingType = "TestClass"
                };

                mockContext.SyntaxContext = syntaxContext;
                Console.WriteLine($"✓ 语法上下文添加成功");
                Console.WriteLine($"  - 当前节点: {syntaxContext.CurrentNode}");
                Console.WriteLine($"  - 包含方法: {syntaxContext.ContainingMember}");
                Console.WriteLine($"  - 包含类型: {syntaxContext.ContainingType}");

                await Task.Delay(100); // 模拟异步操作
                Console.WriteLine("✅ 上下文提取测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 上下文提取测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 测试 LLM 提供者
        /// </summary>
        private static async Task TestLlmProviderAsync()
        {
            Console.WriteLine("🤖 测试 LLM 提供者...");

            try
            {
                // 测试模拟提供者
                var mockProvider = new MockLlmProvider();
                Console.WriteLine($"✓ 模拟提供者创建成功");
                Console.WriteLine($"  - 提供者名称: {mockProvider.Name}");
                Console.WriteLine($"  - 可用状态: {mockProvider.IsAvailable}");

                // 测试发送请求
                var testPrompt = "请补全以下 C# 代码：\nvar result = ";
                var response = await mockProvider.SendAsync(testPrompt);
                
                Console.WriteLine($"✓ 模拟请求发送成功");
                Console.WriteLine($"  - 提示词长度: {testPrompt.Length} 字符");
                Console.WriteLine($"  - 响应长度: {response?.Length ?? 0} 字符");
                Console.WriteLine($"  - 响应预览: {response?.Substring(0, Math.Min(50, response?.Length ?? 0))}...");

                // 测试工厂方法
                var defaultOptions = LlmProviderFactory.GetDefaultOptions(LlmProviderType.Ollama);
                Console.WriteLine($"✓ 默认选项获取成功");
                Console.WriteLine($"  - API 基地址: {defaultOptions.ApiBase}");
                Console.WriteLine($"  - 默认模型: {defaultOptions.Model}");
                Console.WriteLine($"  - 超时时间: {defaultOptions.TimeoutSeconds} 秒");

                Console.WriteLine("✅ LLM 提供者测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ LLM 提供者测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 运行单元测试
        /// </summary>
        private static void RunUnitTests()
        {
            Console.WriteLine("🧪 运行单元测试...");

            try
            {
                // 运行现有的单元测试
                AICompletionSourceTests.RunAllTests();
                Console.WriteLine("✅ 单元测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 单元测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 程序入口点（用于独立运行）
        /// </summary>
        public static async Task Main(string[] args)
        {
            await RunValidationAsync();
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
