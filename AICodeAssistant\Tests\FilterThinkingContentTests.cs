using System;
using Xunit;

namespace AICodeAssistant.Tests
{
    /// <summary>
    /// 测试思考内容过滤功能
    /// </summary>
    public class FilterThinkingContentTests
    {
        [Fact]
        public void FilterThinkingContent_ShouldRemoveThinkTags()
        {
            // Arrange
            var content = @"这是正文内容。

<think>
这是思考过程，应该被移除。
包含多行内容。
</think>

这是更多正文内容。";

            var expected = @"这是正文内容。

这是更多正文内容。";

            // Act
            var result = FilterThinkingContentHelper(content);

            // Assert
            Assert.Equal(expected.Trim(), result.Trim());
        }

        [Fact]
        public void FilterThinkingContent_ShouldHandleMultipleThinkTags()
        {
            // Arrange
            var content = @"开始内容。

<think>第一个思考</think>

中间内容。

<think>
第二个思考
多行内容
</think>

结束内容。";

            var expected = @"开始内容。

中间内容。

结束内容。";

            // Act
            var result = FilterThinkingContentHelper(content);

            // Assert
            Assert.Equal(expected.Trim(), result.Trim());
        }

        [Fact]
        public void FilterThinkingContent_ShouldHandleEmptyContent()
        {
            // Act & Assert
            Assert.Equal("", FilterThinkingContentHelper(""));
            Assert.Equal("", FilterThinkingContentHelper(null));
        }

        [Fact]
        public void FilterThinkingContent_ShouldHandleContentWithoutThinkTags()
        {
            // Arrange
            var content = "这是没有思考标签的正常内容。";

            // Act
            var result = FilterThinkingContentHelper(content);

            // Assert
            Assert.Equal(content, result);
        }

        /// <summary>
        /// 辅助方法，模拟 FilterThinkingContent 的逻辑
        /// </summary>
        private string FilterThinkingContentHelper(string content)
        {
            if (string.IsNullOrEmpty(content))
                return content;

            try
            {
                // 使用正则表达式移除 <think>...</think> 标签及其内容
                var thinkPattern = @"<think\s*>.*?</think\s*>";
                var filteredContent = System.Text.RegularExpressions.Regex.Replace(
                    content,
                    thinkPattern,
                    "",
                    System.Text.RegularExpressions.RegexOptions.Singleline |
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                // 清理多余的空行和空白字符
                filteredContent = System.Text.RegularExpressions.Regex.Replace(
                    filteredContent,
                    @"\n\s*\n\s*\n",
                    "\n\n");

                return filteredContent.Trim();
            }
            catch (Exception)
            {
                return content; // 如果过滤失败，返回原始内容
            }
        }
    }
}
