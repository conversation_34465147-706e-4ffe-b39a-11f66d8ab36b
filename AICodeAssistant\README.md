# OllamaAICodeAssistant

一个强大的 Visual Studio 扩展，集成了 AI 代码补全和智能聊天功能，支持 OpenAI 和 Ollama 两种 LLM 提供者。

## 功能特性

### 🚀 智能代码补全
- **实时补全建议**：基于上下文的智能代码补全
- **多语言支持**：支持 C#、VB.NET、C++、JavaScript、TypeScript、Python 等
- **语法感知**：集成 Roslyn 分析器，提供精确的语法上下文
- **流式响应**：实时显示 AI 生成的代码建议

### 💬 即时对话助手
- **侧边栏聊天**：常驻聊天窗口，随时与 AI 对话
- **上下文感知**：自动获取当前文件和选中代码作为上下文
- **快捷命令**：支持 `/explain`、`/refactor`、`/test`、`/doc` 等命令
- **聊天历史**：自动保存和恢复聊天记录
- **Markdown 导出**：支持将聊天记录导出为 Markdown 文件

### 🔧 灵活配置
- **双提供者支持**：支持 OpenAI API 和本地 Ollama
- **参数调节**：可调节温度、最大 Token 数、上下文长度等
- **安全保护**：自动过滤敏感数据（API 密钥、密码等）
- **个性化设置**：丰富的配置选项满足不同需求

## 安装步骤

### 方式一：从 VSIX 文件安装
1. 下载最新的 `.vsix` 文件
2. 双击 `.vsix` 文件或在 Visual Studio 中选择 "扩展" > "管理扩展" > "从文件安装"
3. 重启 Visual Studio

### 方式二：从源码构建
1. 克隆仓库：
   ```bash
   git clone <repository-url>
   cd AICodeAssistant
   ```

2. 使用 Visual Studio 2022 打开 `AICodeAssistant.sln`

3. 确保已安装以下组件：
   - Visual Studio SDK
   - .NET Framework 4.8 开发工具

4. 构建项目（Ctrl+Shift+B）

5. 按 F5 启动实验实例进行测试

## 配置指南

### OpenAI 配置
1. 打开 Visual Studio
2. 选择 "工具" > "选项" > "AI Code Assistant"
3. 选择 "OpenAI" 作为提供者
4. 输入您的 OpenAI API Key
5. （可选）自定义 API Base URL
6. 选择模型（gpt-3.5-turbo、gpt-4 等）
7. 点击 "测试连接" 验证配置
8. 保存设置

### Ollama 配置
1. 首先安装并启动 Ollama：
   ```bash
   # 安装 Ollama
   curl -fsSL https://ollama.ai/install.sh | sh
   
   # 下载模型（推荐）
   ollama pull codellama
   ollama pull llama2
   ```

2. 在 Visual Studio 中：
   - 选择 "Ollama" 作为提供者
   - 确认 API Base URL（默认：http://localhost:11434）
   - 选择或输入模型名称
   - 测试连接
   - 保存设置

## 使用方法

### 智能代码补全
1. 在代码编辑器中正常编写代码
2. 当需要 AI 建议时，按 `Ctrl+Space` 触发补全
3. AI 建议会出现在 IntelliSense 列表中，标记为 "(AI)"
4. 选择合适的建议并按 Tab 或 Enter 插入

### 聊天助手
1. 打开聊天窗口：
   - 方式一：菜单 "视图" > "其他窗口" > "AI Code Assistant"
   - 方式二：快捷键 `Ctrl+Alt+A`

2. 基本对话：
   - 在输入框中输入问题
   - 按 Enter 或点击 "发送" 按钮
   - AI 会基于当前文件上下文回答

3. 使用快捷命令：
   ```
   /explain    - 解释选中的代码
   /refactor   - 重构选中的代码
   /test       - 为选中的代码生成单元测试
   /doc        - 为选中的代码生成文档注释
   /search     - 搜索相关代码模式
   /clear      - 清空聊天记录
   ```

4. 导出聊天记录：
   - 点击工具栏中的导出按钮
   - 聊天记录将以 Markdown 格式保存到桌面

## 支持的编程语言

- **C#** - 完整支持，包括语法分析
- **VB.NET** - 基础支持
- **C/C++** - 基础支持
- **JavaScript/TypeScript** - 基础支持
- **Python** - 基础支持
- **Java** - 基础支持
- **XML/XAML** - 基础支持
- **JSON** - 基础支持
- **HTML/CSS** - 基础支持
- **SQL** - 基础支持

## 故障排除

### 常见问题

**Q: AI 补全不工作**
A: 检查以下项目：
- 确认 LLM 提供者配置正确
- 测试网络连接
- 检查 API 密钥是否有效
- 确认模型是否可用

**Q: Ollama 连接失败**
A: 
- 确认 Ollama 服务正在运行：`ollama serve`
- 检查端口是否被占用（默认 11434）
- 确认模型已下载：`ollama list`

**Q: 聊天记录丢失**
A: 
- 聊天记录保存在：`%AppData%\OllamaAICodeAssistant\chatlog.json`
- 确认该目录有写入权限
- 检查磁盘空间是否充足

**Q: 扩展加载失败**
A:
- 检查 Visual Studio 版本兼容性（需要 VS 2022）
- 确认 .NET Framework 4.8 已安装
- 查看 Visual Studio 错误日志

### 性能优化

1. **减少上下文长度**：在设置中降低上下文行数
2. **调整温度参数**：较低的温度值可提高响应速度
3. **限制最大 Token 数**：减少生成的最大长度
4. **使用本地模型**：Ollama 通常比 API 调用更快

## 开发者信息

### 技术栈
- **.NET Framework 4.8**
- **Visual Studio SDK 17.x**
- **Community.VisualStudio.Toolkit**
- **Microsoft.CodeAnalysis (Roslyn)**
- **System.Text.Json**
- **WPF (XAML)**

### 架构设计
- **服务层**：统一的 LLM 提供者接口
- **功能层**：代码补全和聊天功能的独立实现
- **UI层**：WPF 用户界面和设置页面
- **安全层**：敏感数据过滤和保护

### 贡献指南
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持 OpenAI 和 Ollama 双提供者
- 实现智能代码补全功能
- 实现即时聊天助手
- 提供丰富的配置选项
- 支持敏感数据过滤

---

如有问题或建议，请提交 Issue 或联系开发团队。
