using System;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.VisualStudio.Shell;

namespace AICodeAssistant.Services
{
    /// <summary>
    /// OpenAI API 提供者实现
    /// </summary>
    public class OpenAiProvider : ILlmProvider, IDisposable
    {
        private readonly LlmOptions _options;
        private readonly HttpClient _httpClient;

        public string Name => "OpenAI";
        public bool IsAvailable => !string.IsNullOrEmpty(_options.ApiKey);

        public OpenAiProvider(LlmOptions options)
        {
            _options = options ?? throw new ArgumentNullException(nameof(options));
            _httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromSeconds(_options.TimeoutSeconds)
            };

            if (!string.IsNullOrEmpty(_options.ApiKey))
            {
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_options.ApiKey}");
            }
        }

        public async Task<string> SendAsync(string prompt, bool stream = false, IProgress<string> progress = null, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(_options.ApiKey))
                throw new InvalidOperationException("OpenAI API Key 未配置");

            var apiUrl = string.IsNullOrEmpty(_options.ApiBase) 
                ? "https://api.openai.com/v1/chat/completions" 
                : $"{_options.ApiBase.TrimEnd('/')}/v1/chat/completions";

            var requestBody = new
            {
                model = string.IsNullOrEmpty(_options.Model) ? "gpt-3.5-turbo" : _options.Model,
                messages = new[]
                {
                    new { role = "user", content = prompt }
                },
                temperature = _options.Temperature,
                max_tokens = _options.MaxTokens,
                stream = stream
            };

            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            try
            {
                if (stream && progress != null)
                {
                    return await SendStreamRequestAsync(apiUrl, content, progress, cancellationToken).ConfigureAwait(false);
                }
                else
                {
                    return await SendNormalRequestAsync(apiUrl, content, cancellationToken).ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("OpenAiProvider", $"API 调用失败: {ex.Message}");
                throw;
            }
        }

        private async Task<string> SendNormalRequestAsync(string apiUrl, StringContent content, CancellationToken cancellationToken)
        {
            using var response = await _httpClient.PostAsync(apiUrl, content, cancellationToken).ConfigureAwait(false);
            response.EnsureSuccessStatusCode();

            var responseJson = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
            using var document = JsonDocument.Parse(responseJson);
            
            var choices = document.RootElement.GetProperty("choices");
            if (choices.GetArrayLength() > 0)
            {
                var message = choices[0].GetProperty("message");
                return message.GetProperty("content").GetString() ?? string.Empty;
            }

            return string.Empty;
        }

        private async Task<string> SendStreamRequestAsync(string apiUrl, StringContent content, IProgress<string> progress, CancellationToken cancellationToken)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, apiUrl) { Content = content };
            using var response = await _httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
            response.EnsureSuccessStatusCode();

            var fullResponse = new StringBuilder();
            using var stream = await response.Content.ReadAsStreamAsync().ConfigureAwait(false);
            using var reader = new StreamReader(stream);

            string line;
            while ((line = await reader.ReadLineAsync().ConfigureAwait(false)) != null)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                if (line.StartsWith("data: "))
                {
                    var data = line.Substring(6);
                    if (data == "[DONE]")
                        break;

                    try
                    {
                        using var document = JsonDocument.Parse(data);
                        var choices = document.RootElement.GetProperty("choices");
                        if (choices.GetArrayLength() > 0)
                        {
                            var delta = choices[0].GetProperty("delta");
                            if (delta.TryGetProperty("content", out var contentElement))
                            {
                                var chunk = contentElement.GetString();
                                if (!string.IsNullOrEmpty(chunk))
                                {
                                    fullResponse.Append(chunk);
                                    progress?.Report(chunk);
                                }
                            }
                        }
                    }
                    catch (JsonException)
                    {
                        // 忽略无效的 JSON 行
                    }
                }
            }

            return fullResponse.ToString();
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
