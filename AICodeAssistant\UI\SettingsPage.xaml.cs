using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.VisualStudio.Shell;
using AICodeAssistant.Services;

namespace AICodeAssistant.UI
{
    public partial class SettingsPage : UserControl, INotifyPropertyChanged
    {
        private AppSettings _settings;
        private ObservableCollection<string> _availableModels;
        private string _selectedModel;

        public ObservableCollection<string> AvailableModels
        {
            get => _availableModels;
            set
            {
                _availableModels = value;
                OnPropertyChanged(nameof(AvailableModels));
            }
        }

        public string SelectedModel
        {
            get => _selectedModel;
            set
            {
                if (_selectedModel != value)
                {
                    _selectedModel = value;
                    OnPropertyChanged(nameof(SelectedModel));
                    ActivityLog.LogInformation("SettingsPage", $"选择的模型已更改为: {value}");
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        public SettingsPage()
        {
            _availableModels = new ObservableCollection<string>();
            InitializeComponent();
            DataContext = this;
            InitializeAsync().FireAndForget();
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private async Task InitializeAsync()
        {
            await LoadSettingsAsync().ConfigureAwait(false);
            await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();

            SetupEventHandlers();
            UpdateUI();

            // 移除自动加载模型功能，仅在用户主动点击"刷新模型"时才加载
            ActivityLog.LogInformation("SettingsPage", "设置页面初始化完成，等待用户手动刷新模型列表");
        }

        private void SetupEventHandlers()
        {
            ProviderComboBox.SelectionChanged += ProviderComboBox_SelectionChanged;
            ContextLinesSlider.ValueChanged += (s, e) => ContextLinesLabel.Text = $"{(int)e.NewValue} 行";
            MaxTokensSlider.ValueChanged += (s, e) => MaxTokensLabel.Text = $"{(int)e.NewValue} tokens";
            TemperatureSlider.ValueChanged += (s, e) => TemperatureLabel.Text = e.NewValue.ToString("F1");
            MaxContextLengthSlider.ValueChanged += (s, e) =>
            {
                var value = (int)e.NewValue;
                var displayText = value >= 1024 ? $"{value} tokens ({value / 1024}K)" : $"{value} tokens";
                MaxContextLengthLabel.Text = displayText;
            };
            MaxOutputTokensControl.ValueChanged += (s, e) => MaxOutputTokensDisplay.Text = $"{(int)e.NewValue} tokens";

            // 添加模型选择变化事件处理
            OllamaModelBox.SelectionChanged += OllamaModelBox_SelectionChanged;
            // ComboBox没有TextChanged事件，使用LostFocus事件来处理手动输入
            OllamaModelBox.LostFocus += OllamaModelBox_LostFocus;
        }

        private void OllamaModelBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            if (OllamaModelBox.SelectedValue is string selectedModel && !string.IsNullOrEmpty(selectedModel))
            {
                SelectedModel = selectedModel;
                ActivityLog.LogInformation("SettingsPage", $"通过选择更改模型为: {selectedModel}");

                // 立即更新UI显示
                UpdateModelStatusDisplay(selectedModel);
            }
        }

        private void OllamaModelBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (OllamaModelBox.IsEditable && !string.IsNullOrEmpty(OllamaModelBox.Text))
            {
                SelectedModel = OllamaModelBox.Text;
                ActivityLog.LogInformation("SettingsPage", $"通过输入更改模型为: {OllamaModelBox.Text}");

                // 立即更新UI显示
                UpdateModelStatusDisplay(OllamaModelBox.Text);
            }
        }

        /// <summary>
        /// 更新模型状态显示
        /// </summary>
        /// <param name="modelName">模型名称</param>
        private void UpdateModelStatusDisplay(string modelName)
        {
            if (!string.IsNullOrEmpty(modelName))
            {
                ModelStatusLabel.Text = $"当前选择: {modelName}";
                ModelStatusLabel.Foreground = System.Windows.Media.Brushes.Blue;
            }
        }

        private void ProviderComboBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            var selectedItem = ProviderComboBox.SelectedItem as ComboBoxItem;
            var providerName = selectedItem?.Tag?.ToString();

            if (providerName == "OpenAI")
            {
                OpenAISettings.Visibility = Visibility.Visible;
                OllamaSettings.Visibility = Visibility.Collapsed;
            }
            else
            {
                OpenAISettings.Visibility = Visibility.Collapsed;
                OllamaSettings.Visibility = Visibility.Visible;

                // 移除自动加载模型列表，改为显示提示信息
                ModelStatusLabel.Text = "请点击\"刷新模型\"按钮获取可用模型列表";
                ModelStatusLabel.Foreground = System.Windows.Media.Brushes.Gray;
                ActivityLog.LogInformation("SettingsPage", "切换到 Ollama 提供者，等待用户手动刷新模型");
            }
        }

        private void UpdateUI()
        {
            if (_settings == null) return;

            // 设置提供者
            ProviderComboBox.SelectedIndex = _settings.ProviderType == LlmProviderType.OpenAI ? 1 : 0;

            // OpenAI 设置
            OpenAIApiKeyBox.Password = _settings.OpenAI.ApiKey;
            OpenAIApiBaseBox.Text = _settings.OpenAI.ApiBase;
            SetComboBoxValue(OpenAIModelBox, _settings.OpenAI.Model);

            // Ollama 设置
            OllamaApiBaseBox.Text = _settings.Ollama.ApiBase;

            // 确保模型列表中包含当前设置的模型
            if (!string.IsNullOrEmpty(_settings.Ollama.Model))
            {
                if (!AvailableModels.Contains(_settings.Ollama.Model))
                {
                    AvailableModels.Add(_settings.Ollama.Model);
                }
                SelectedModel = _settings.Ollama.Model;

                // 立即更新UI显示
                UpdateModelStatusDisplay(_settings.Ollama.Model);

                // 确保ComboBox显示正确的值
                OllamaModelBox.Text = _settings.Ollama.Model;
            }
            else
            {
                SelectedModel = Constants.Defaults.DefaultOllamaModel;
                UpdateModelStatusDisplay(Constants.Defaults.DefaultOllamaModel);
                OllamaModelBox.Text = Constants.Defaults.DefaultOllamaModel;
            }

            // 补全设置
            EnableCompletionCheckBox.IsChecked = _settings.Completion.Enabled;
            ContextLinesSlider.Value = _settings.Completion.ContextLines;
            MaxTokensSlider.Value = _settings.Completion.MaxTokens;
            TemperatureSlider.Value = _settings.Completion.Temperature;

            // 聊天设置
            MaxContextLengthSlider.Value = _settings.Chat.MaxContextLength;
            MaxOutputTokensControl.Value = _settings.Chat.MaxOutputTokens;
            AutoSaveChatCheckBox.IsChecked = _settings.Chat.AutoSave;

            // 安全设置
            MaskSensitiveDataCheckBox.IsChecked = _settings.Security.MaskSensitiveData;
        }

        private void SetComboBoxValue(ComboBox comboBox, string value)
        {
            foreach (ComboBoxItem item in comboBox.Items)
            {
                if (item.Content.ToString() == value)
                {
                    comboBox.SelectedItem = item;
                    break;
                }
            }
        }

        private void Save_Click(object sender, RoutedEventArgs e)
        {
            _ = SaveSettingsAsync();
        }

        private void Reset_Click(object sender, RoutedEventArgs e)
        {
            ResetSettings();
        }

        private void ResetSettings()
        {
            try
            {
                // 检查确认复选框
                if (ConfirmResetCheckBox?.IsChecked != true)
                {
                    ShowMessage("重置取消", "请先勾选确认复选框再点击重置", true);
                    return;
                }

                // 重置设置
                _settings = AppSettings.CreateDefault();
                UpdateUI();
                _ = SaveSettingsAsync();

                // 重置后取消勾选确认复选框
                ConfirmResetCheckBox.IsChecked = false;

                ShowMessage("重置完成", "所有设置已重置为默认值", false);
                ActivityLog.LogInformation("SettingsPage", "设置已重置为默认值");
            }
            catch (Exception ex)
            {
                ShowMessage("重置失败", ex.Message, true);
                ActivityLog.LogError("SettingsPage", $"重置设置失败: {ex.Message}");
            }
        }

        private void TestConnection_Click(object sender, RoutedEventArgs e)
        {
            _ = TestConnectionAsync();
        }

        private void RefreshModels_Click(object sender, RoutedEventArgs e)
        {
            _ = LoadOllamaModelsAsync(forceRefresh: true);
        }

        private async Task TestConnectionAsync()
        {
            try
            {
                await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                TestConnectionButton.IsEnabled = false;
                TestConnectionButton.Content = "测试中...";

                var providerType = ProviderComboBox.SelectedIndex == 1 ? LlmProviderType.OpenAI : LlmProviderType.Ollama;
                var options = CreateLlmOptions(providerType);

                if (providerType == LlmProviderType.Ollama)
                {
                    var ollamaProvider = new OllamaProvider(options);
                    var testResult = await ollamaProvider.TestConnectionAsync().ConfigureAwait(false);

                    await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();

                    if (testResult.IsSuccess)
                    {
                        // 使用非阻塞方式显示消息
                        ShowMessage("连接测试成功", testResult.Message, false);

                        // 连接成功后自动刷新模型列表
                        _ = LoadOllamaModelsAsync(forceRefresh: true);
                    }
                    else
                    {
                        ShowMessage("连接测试失败", testResult.Message, true);
                    }
                }
                else
                {
                    // OpenAI 连接测试保持原有逻辑
                    var provider = LlmProviderFactory.CreateProvider(providerType, options);
                    if (provider.IsAvailable)
                    {
                        ShowMessage("连接测试", "连接成功！", false);
                    }
                    else
                    {
                        ShowMessage("连接测试", "连接失败，请检查设置", true);
                    }
                }
            }
            catch (Exception ex)
            {
                ShowMessage("连接测试", $"连接失败: {ex.Message}", true);
                ActivityLog.LogError("SettingsPage", $"连接测试异常: {ex.Message}");
            }
            finally
            {
                await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                TestConnectionButton.IsEnabled = true;
                TestConnectionButton.Content = "测试连接";
            }
        }

        private LlmOptions CreateLlmOptions(LlmProviderType providerType)
        {
            return providerType switch
            {
                LlmProviderType.OpenAI => new LlmOptions
                {
                    ApiKey = OpenAIApiKeyBox.Password,
                    ApiBase = OpenAIApiBaseBox.Text,
                    Model = (OpenAIModelBox.SelectedItem as ComboBoxItem)?.Content?.ToString() ?? "gpt-3.5-turbo",
                    Temperature = TemperatureSlider.Value,
                    MaxTokens = (int)MaxTokensSlider.Value,
                    TimeoutSeconds = 30
                },
                LlmProviderType.Ollama => new LlmOptions
                {
                    ApiBase = OllamaApiBaseBox.Text,
                    Model = SelectedModel ?? Constants.Defaults.DefaultOllamaModel,
                    Temperature = TemperatureSlider.Value,
                    MaxTokens = (int)MaxTokensSlider.Value,
                    TimeoutSeconds = 60
                },
                _ => new LlmOptions()
            };
        }

        private Task LoadSettingsAsync()
        {
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var folderPath = Path.Combine(appDataPath, Constants.Config.AppDataFolder);
                var filePath = Path.Combine(folderPath, Constants.Config.SettingsFileName);

                if (File.Exists(filePath))
                {
                    var json = File.ReadAllText(filePath);
                    _settings = JsonSerializer.Deserialize<AppSettings>(json) ?? AppSettings.CreateDefault();
                }
                else
                {
                    _settings = AppSettings.CreateDefault();
                }
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("SettingsPage", $"加载设置失败: {ex.Message}");
                _settings = AppSettings.CreateDefault();
                return Task.CompletedTask;
            }
        }

        private async Task SaveSettingsAsync()
        {
            try
            {
                await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                // 更新设置对象
                var selectedProvider = ProviderComboBox.SelectedItem as ComboBoxItem;
                _settings.ProviderType = selectedProvider?.Tag?.ToString() == "OpenAI" ? LlmProviderType.OpenAI : LlmProviderType.Ollama;

                // OpenAI 设置
                _settings.OpenAI.ApiKey = OpenAIApiKeyBox.Password;
                _settings.OpenAI.ApiBase = OpenAIApiBaseBox.Text;
                _settings.OpenAI.Model = (OpenAIModelBox.SelectedItem as ComboBoxItem)?.Content?.ToString() ?? "gpt-3.5-turbo";

                // Ollama 设置
                _settings.Ollama.ApiBase = OllamaApiBaseBox.Text;

                // 确保保存选择的模型
                var selectedOllamaModel = SelectedModel;
                if (string.IsNullOrEmpty(selectedOllamaModel))
                {
                    selectedOllamaModel = OllamaModelBox.Text; // 如果是手动输入的模型
                }
                if (string.IsNullOrEmpty(selectedOllamaModel))
                {
                    selectedOllamaModel = Constants.Defaults.DefaultOllamaModel;
                }
                _settings.Ollama.Model = selectedOllamaModel;

                // 补全设置
                _settings.Completion.Enabled = EnableCompletionCheckBox.IsChecked ?? true;
                _settings.Completion.ContextLines = (int)ContextLinesSlider.Value;
                _settings.Completion.MaxTokens = (int)MaxTokensSlider.Value;
                _settings.Completion.Temperature = TemperatureSlider.Value;

                // 聊天设置
                _settings.Chat.MaxContextLength = (int)MaxContextLengthSlider.Value;
                _settings.Chat.MaxOutputTokens = (int)MaxOutputTokensControl.Value;
                _settings.Chat.AutoSave = AutoSaveChatCheckBox.IsChecked ?? true;

                // 安全设置
                _settings.Security.MaskSensitiveData = MaskSensitiveDataCheckBox.IsChecked ?? true;

                // 保存到文件
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var folderPath = Path.Combine(appDataPath, Constants.Config.AppDataFolder);

                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }

                var filePath = Path.Combine(folderPath, Constants.Config.SettingsFileName);
                var json = JsonSerializer.Serialize(_settings, new JsonSerializerOptions { WriteIndented = true });

                File.WriteAllText(filePath, json);

                // 记录保存的设置信息
                ActivityLog.LogInformation("SettingsPage", $"设置已保存到: {filePath}");
                ActivityLog.LogInformation("SettingsPage", $"提供者类型: {_settings.ProviderType}");

                string successMessage = "设置已保存";
                if (_settings.ProviderType == LlmProviderType.Ollama)
                {
                    ActivityLog.LogInformation("SettingsPage", $"Ollama API Base: {_settings.Ollama.ApiBase}");
                    ActivityLog.LogInformation("SettingsPage", $"Ollama Model: {_settings.Ollama.Model}");
                    successMessage = $"设置已保存\n当前选择的模型: {_settings.Ollama.Model}";
                }
                else
                {
                    ActivityLog.LogInformation("SettingsPage", $"OpenAI Model: {_settings.OpenAI.Model}");
                    successMessage = $"设置已保存\n当前选择的模型: {_settings.OpenAI.Model}";
                }

                // 使用非阻塞方式显示成功消息
                ShowMessage("设置保存成功", successMessage, false);
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("SettingsPage", $"保存设置失败: {ex.Message}");
                ShowMessage("保存失败", ex.Message, true);
                throw;
            }
        }

        /// <summary>
        /// 加载 Ollama 模型列表
        /// </summary>
        /// <param name="forceRefresh">是否强制刷新</param>
        private async Task LoadOllamaModelsAsync(bool forceRefresh = false)
        {
            try
            {
                await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();

                if (forceRefresh)
                {
                    RefreshModelsButton.IsEnabled = false;
                    RefreshModelsButton.Content = "加载中...";
                    ModelStatusLabel.Text = "正在获取模型列表...";
                }

                var apiBase = OllamaApiBaseBox.Text;
                if (string.IsNullOrWhiteSpace(apiBase))
                {
                    apiBase = Constants.Ollama.DefaultApiBase;
                }

                var options = new LlmOptions
                {
                    ApiBase = apiBase,
                    TimeoutSeconds = 10 // 较短的超时时间用于模型获取
                };

                var ollamaProvider = new OllamaProvider(options);
                ActivityLog.LogInformation("SettingsPage", $"开始获取 Ollama 模型列表，API Base: {options.ApiBase}");

                var models = await ollamaProvider.GetAvailableModelsAsync().ConfigureAwait(false);

                await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();

                // 清空现有模型列表
                AvailableModels.Clear();

                if (models?.Any() == true)
                {
                    // 添加获取到的模型
                    foreach (var model in models)
                    {
                        AvailableModels.Add(model);
                    }

                    // 如果当前选择的模型不在列表中，添加它
                    if (!string.IsNullOrEmpty(SelectedModel) && !AvailableModels.Contains(SelectedModel))
                    {
                        AvailableModels.Insert(0, SelectedModel);
                    }

                    // 如果没有选择模型，选择第一个
                    if (string.IsNullOrEmpty(SelectedModel) && AvailableModels.Any())
                    {
                        SelectedModel = AvailableModels.First();
                        OllamaModelBox.Text = SelectedModel;
                    }

                    // 检查是否是从缓存加载的
                    var isFromCache = models.Count == 1 && models.First() == Constants.Defaults.DefaultOllamaModel;
                    if (isFromCache)
                    {
                        ModelStatusLabel.Text = "使用缓存的模型列表";
                        ModelStatusLabel.Foreground = System.Windows.Media.Brushes.Orange;
                    }
                    else
                    {
                        ModelStatusLabel.Text = $"已加载 {models.Count} 个模型";
                        ModelStatusLabel.Foreground = System.Windows.Media.Brushes.Green;
                    }

                    // 如果有选择的模型，显示当前选择
                    if (!string.IsNullOrEmpty(SelectedModel))
                    {
                        ModelStatusLabel.Text += $" | 当前选择: {SelectedModel}";
                    }

                    ActivityLog.LogInformation("SettingsPage", $"成功加载 {models.Count} 个 Ollama 模型");
                }
                else
                {
                    // 如果没有获取到模型，添加默认模型
                    AvailableModels.Add(Constants.Defaults.DefaultOllamaModel);

                    if (string.IsNullOrEmpty(SelectedModel))
                    {
                        SelectedModel = Constants.Defaults.DefaultOllamaModel;
                    }

                    ModelStatusLabel.Text = Constants.Ollama.ModelNotAvailableText;
                    ModelStatusLabel.Foreground = System.Windows.Media.Brushes.Orange;
                }
            }
            catch (Exception ex)
            {
                await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();

                // 发生异常时，确保至少有默认模型
                if (!AvailableModels.Any())
                {
                    AvailableModels.Add(Constants.Defaults.DefaultOllamaModel);
                }

                if (string.IsNullOrEmpty(SelectedModel))
                {
                    SelectedModel = Constants.Defaults.DefaultOllamaModel;
                }

                ModelStatusLabel.Text = $"加载失败: {ex.Message}";
                ModelStatusLabel.Foreground = System.Windows.Media.Brushes.Red;

                ActivityLog.LogError("SettingsPage", $"加载 Ollama 模型失败: {ex.Message}");
            }
            finally
            {
                await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                RefreshModelsButton.IsEnabled = true;
                RefreshModelsButton.Content = "刷新模型";
            }
        }

        /// <summary>
        /// 使用内嵌状态区域显示消息，完全避免模态对话框
        /// </summary>
        /// <param name="title">标题</param>
        /// <param name="message">消息</param>
        /// <param name="isError">是否为错误消息</param>
        private void ShowMessage(string title, string message, bool isError)
        {
            try
            {
                var statusMessage = $"{title}: {message}";

                // 记录到活动日志
                if (isError)
                {
                    ActivityLog.LogError("SettingsPage", statusMessage);
                }
                else
                {
                    ActivityLog.LogInformation("SettingsPage", statusMessage);
                }

                // 更新UI状态显示
                StatusMessage.Text = statusMessage;
                StatusIcon.Text = isError ? "⚠" : "ℹ";
                StatusBorder.Background = isError ?
                    System.Windows.Media.Brushes.LightPink :
                    System.Windows.Media.Brushes.LightGreen;
                StatusBorder.BorderBrush = isError ?
                    System.Windows.Media.Brushes.Red :
                    System.Windows.Media.Brushes.Green;
                StatusIcon.Foreground = isError ?
                    System.Windows.Media.Brushes.Red :
                    System.Windows.Media.Brushes.Green;
                StatusMessage.Foreground = isError ?
                    System.Windows.Media.Brushes.DarkRed :
                    System.Windows.Media.Brushes.DarkGreen;

                StatusBorder.Visibility = Visibility.Visible;

                // 3秒后自动隐藏状态消息
                _ = Task.Run(async () =>
                {
                    await Task.Delay(3000);
                    await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                    StatusBorder.Visibility = Visibility.Collapsed;
                });
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("SettingsPage", $"显示消息失败: {ex.Message}");
            }
        }
    }
}
