using System;
using System.ComponentModel.Composition;
using System.Windows.Input;
using System.Windows.Media;
using Microsoft.VisualStudio.Text;
using Microsoft.VisualStudio.Text.Editor;
using Microsoft.VisualStudio.Text.Formatting;
using Microsoft.VisualStudio.Utilities;
using Microsoft.VisualStudio.Shell;
using System.Threading.Tasks;
using System.Threading;

namespace AICodeAssistant.Features.Completion
{
    /// <summary>
    /// Ghost Text控制器
    /// 负责Ghost Text的显示、交互和生命周期管理
    /// </summary>
    // Disabled to avoid interference with Pure Ghost Text
    // [Export(typeof(IWpfTextViewCreationListener))]
    [ContentType("code")]
    [ContentType("text")]
    [TextViewRole(PredefinedTextViewRoles.Editable)]
    public class GhostTextController : IWpfTextViewCreationListener
    {
        public void TextViewCreated(IWpfTextView textView)
        {
            try
            {
                // 为每个文本视图创建Ghost Text管理器
                var manager = new GhostTextManager(textView);
                textView.Properties.AddProperty(typeof(GhostTextManager), manager);

                ActivityLog.LogInformation("GhostTextController", "为文本视图创建Ghost Text管理器");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextController", $"创建Ghost Text管理器失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Ghost Text管理器
    /// 管理单个文本视图的Ghost Text显示和交互
    /// </summary>
    public class GhostTextManager : IDisposable
    {
        private readonly IWpfTextView _textView;
        private readonly GhostTextCompletionProvider _provider;
        
        private IAdornmentLayer _adornmentLayer;
        private IGhostTextSuggestion _currentSuggestion;
        private CancellationTokenSource _currentRequest;
        private Timer _triggerTimer;
        
        // 配置参数
        private const int TriggerDelayMs = 1200; // 触发延迟
        private const string AdornmentTag = "GhostText";

        public GhostTextManager(IWpfTextView textView)
        {
            _textView = textView ?? throw new ArgumentNullException(nameof(textView));
            _provider = new GhostTextCompletionProvider();

            // 获取装饰层
            _adornmentLayer = _textView.GetAdornmentLayer("GhostText");

            // 订阅事件
            _textView.TextBuffer.Changed += OnTextBufferChanged;
            _textView.Caret.PositionChanged += OnCaretPositionChanged;
            _textView.KeyDown += OnKeyDown;
            _textView.LostFocus += OnLostFocus;
            _textView.Closed += OnTextViewClosed;

            ActivityLog.LogInformation("GhostTextManager", "Ghost Text管理器初始化完成");
        }

        /// <summary>
        /// 文本缓冲区变化事件
        /// </summary>
        private void OnTextBufferChanged(object sender, TextContentChangedEventArgs e)
        {
            try
            {
                // 清除当前Ghost Text
                ClearGhostText();

                // 如果是用户输入，延迟触发Ghost Text
                if (e.Changes.Count > 0)
                {
                    var change = e.Changes[0];
                    if (!string.IsNullOrEmpty(change.NewText) && change.OldText.Length == 0)
                    {
                        // 这是插入操作，延迟触发Ghost Text
                        ScheduleTriggerGhostText();
                    }
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextManager", $"处理文本变化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 光标位置变化事件
        /// </summary>
        private void OnCaretPositionChanged(object sender, CaretPositionChangedEventArgs e)
        {
            try
            {
                // 如果光标移动，清除Ghost Text
                if (_currentSuggestion != null)
                {
                    var currentPosition = e.NewPosition.BufferPosition;
                    var suggestionPosition = _currentSuggestion.ApplicableSpan.Start;
                    
                    if (currentPosition != suggestionPosition)
                    {
                        ClearGhostText();
                    }
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextManager", $"处理光标变化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 键盘按键事件
        /// </summary>
        private void OnKeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                if (_currentSuggestion == null)
                    return;

                switch (e.Key)
                {
                    case Key.Tab:
                        // Tab键接受Ghost Text
                        AcceptGhostText();
                        e.Handled = true;
                        break;

                    case Key.Escape:
                        // Esc键拒绝Ghost Text
                        ClearGhostText();
                        e.Handled = true;
                        break;

                    case Key.Right:
                        // 右箭头键部分接受Ghost Text
                        if (Keyboard.Modifiers == ModifierKeys.Control)
                        {
                            AcceptPartialGhostText();
                            e.Handled = true;
                        }
                        else
                        {
                            ClearGhostText();
                        }
                        break;

                    case Key.Left:
                    case Key.Up:
                    case Key.Down:
                    case Key.Home:
                    case Key.End:
                        // 其他方向键清除Ghost Text
                        ClearGhostText();
                        break;

                    default:
                        // 其他键继续输入时清除Ghost Text
                        if (char.IsLetterOrDigit((char)KeyInterop.VirtualKeyFromKey(e.Key)) ||
                            e.Key == Key.Space || e.Key == Key.Enter)
                        {
                            ClearGhostText();
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextManager", $"处理按键失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 失去焦点事件
        /// </summary>
        private void OnLostFocus(object sender, EventArgs e)
        {
            ClearGhostText();
        }

        /// <summary>
        /// 文本视图关闭事件
        /// </summary>
        private void OnTextViewClosed(object sender, EventArgs e)
        {
            Dispose();
        }

        /// <summary>
        /// 安排触发Ghost Text（延迟执行）
        /// </summary>
        private void ScheduleTriggerGhostText()
        {
            try
            {
                // 取消之前的定时器
                _triggerTimer?.Dispose();

                // 创建新的定时器
                _triggerTimer = new Timer(async _ =>
                {
                    try
                    {
                        await TriggerGhostTextAsync();
                    }
                    catch (Exception ex)
                    {
                        ActivityLog.LogError("GhostTextManager", $"触发Ghost Text失败: {ex.Message}");
                    }
                }, null, TriggerDelayMs, Timeout.Infinite);
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextManager", $"安排触发Ghost Text失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 触发Ghost Text
        /// </summary>
        private async Task TriggerGhostTextAsync()
        {
            try
            {
                // 取消之前的请求
                _currentRequest?.Cancel();
                _currentRequest = new CancellationTokenSource();

                var caretPosition = _textView.Caret.Position.BufferPosition;

                // 获取Ghost Text建议
                var suggestion = await _provider.GetGhostTextAsync(
                    _textView, caretPosition, _currentRequest.Token);

                if (suggestion != null && !string.IsNullOrEmpty(suggestion.Text))
                {
                    // 在UI线程上显示Ghost Text
                    await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                    ShowGhostText(suggestion);
                }
            }
            catch (OperationCanceledException)
            {
                // 请求被取消，忽略
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextManager", $"触发Ghost Text异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示Ghost Text
        /// </summary>
        private void ShowGhostText(IGhostTextSuggestion suggestion)
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                // 清除之前的Ghost Text
                ClearGhostText();

                _currentSuggestion = suggestion;

                // 创建Ghost Text元素
                var textBlock = new System.Windows.Controls.TextBlock
                {
                    Text = suggestion.Text,
                    Foreground = new SolidColorBrush(Colors.Gray),
                    Opacity = 0.5,
                    FontFamily = _textView.FormattedLineSource.DefaultTextProperties.Typeface.FontFamily,
                    FontSize = _textView.FormattedLineSource.DefaultTextProperties.FontRenderingEmSize,
                    FontStyle = System.Windows.FontStyles.Italic,
                    ToolTip = $"AI建议: {suggestion.Text}\n按Tab接受，按Esc拒绝，按Ctrl+→部分接受"
                };

                // 添加到装饰层
                _adornmentLayer.AddAdornment(
                    AdornmentPositioningBehavior.TextRelative,
                    suggestion.ApplicableSpan,
                    AdornmentTag,
                    textBlock,
                    null);

                ActivityLog.LogInformation("GhostTextManager", $"显示Ghost Text: '{suggestion.Text}'");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextManager", $"显示Ghost Text失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 接受Ghost Text
        /// </summary>
        private void AcceptGhostText()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                if (_currentSuggestion == null)
                    return;

                // 插入Ghost Text
                var edit = _textView.TextBuffer.CreateEdit();
                edit.Insert(_currentSuggestion.ApplicableSpan.Start.Position, _currentSuggestion.Text);
                edit.Apply();

                ActivityLog.LogInformation("GhostTextManager", $"接受Ghost Text: '{_currentSuggestion.Text}'");

                // 清除Ghost Text
                ClearGhostText();
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextManager", $"接受Ghost Text失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 部分接受Ghost Text（接受第一个单词）
        /// </summary>
        private void AcceptPartialGhostText()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                if (_currentSuggestion == null)
                    return;

                // 找到第一个单词的结束位置
                var text = _currentSuggestion.Text;
                var spaceIndex = text.IndexOf(' ');
                var partialText = spaceIndex > 0 ? text.Substring(0, spaceIndex) : text;

                // 插入部分文本
                var edit = _textView.TextBuffer.CreateEdit();
                edit.Insert(_currentSuggestion.ApplicableSpan.Start.Position, partialText);
                edit.Apply();

                ActivityLog.LogInformation("GhostTextManager", $"部分接受Ghost Text: '{partialText}'");

                // 清除Ghost Text
                ClearGhostText();
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextManager", $"部分接受Ghost Text失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除Ghost Text
        /// </summary>
        private void ClearGhostText()
        {
            try
            {
                if (_currentSuggestion != null)
                {
                    ThreadHelper.ThrowIfNotOnUIThread();
                    _adornmentLayer.RemoveAllAdornments();
                    _currentSuggestion = null;
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextManager", $"清除Ghost Text失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                // 取消当前请求
                _currentRequest?.Cancel();
                _currentRequest?.Dispose();

                // 停止定时器
                _triggerTimer?.Dispose();

                // 清除Ghost Text
                ClearGhostText();

                // 取消事件订阅
                if (_textView != null)
                {
                    _textView.TextBuffer.Changed -= OnTextBufferChanged;
                    _textView.Caret.PositionChanged -= OnCaretPositionChanged;
                    _textView.KeyDown -= OnKeyDown;
                    _textView.LostFocus -= OnLostFocus;
                    _textView.Closed -= OnTextViewClosed;
                }

                // 释放提供者
                _provider?.Dispose();

                ActivityLog.LogInformation("GhostTextManager", "Ghost Text管理器已释放");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextManager", $"释放资源失败: {ex.Message}");
            }
        }
    }
}
