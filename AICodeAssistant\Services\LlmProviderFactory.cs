using System;
using Microsoft.VisualStudio.Shell;

namespace AICodeAssistant.Services
{
    /// <summary>
    /// LLM 提供者工厂类
    /// </summary>
    public static class LlmProviderFactory
    {
        /// <summary>
        /// 创建 LLM 提供者实例
        /// </summary>
        /// <param name="providerType">提供者类型</param>
        /// <param name="options">配置选项</param>
        /// <returns>LLM 提供者实例</returns>
        public static ILlmProvider CreateProvider(LlmProviderType providerType, LlmOptions options)
        {
            if (options == null)
                throw new ArgumentNullException(nameof(options));

            try
            {
                return providerType switch
                {
                    LlmProviderType.OpenAI => new OpenAiProvider(options),
                    LlmProviderType.Ollama => new OllamaProvider(options),
                    _ => throw new ArgumentException($"不支持的提供者类型: {providerType}", nameof(providerType))
                };
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("LlmProviderFactory", $"创建提供者失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取默认配置选项
        /// </summary>
        /// <param name="providerType">提供者类型</param>
        /// <returns>默认配置选项</returns>
        public static LlmOptions GetDefaultOptions(LlmProviderType providerType)
        {
            return providerType switch
            {
                LlmProviderType.OpenAI => new LlmOptions
                {
                    ApiBase = "https://api.openai.com",
                    Model = "gpt-3.5-turbo",
                    Temperature = 0.7,
                    MaxTokens = 2048,
                    TimeoutSeconds = 30
                },
                LlmProviderType.Ollama => new LlmOptions
                {
                    ApiBase = "http://localhost:11434",
                    Model = "codellama",
                    Temperature = 0.7,
                    MaxTokens = 2048,
                    TimeoutSeconds = 60
                },
                _ => new LlmOptions()
            };
        }
    }
}
