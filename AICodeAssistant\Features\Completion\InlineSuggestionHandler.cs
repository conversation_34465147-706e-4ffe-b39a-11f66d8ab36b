using System;
using System.ComponentModel.Composition;
using System.Threading;
using System.Windows.Input;
using Microsoft.VisualStudio.Language.Intellisense.AsyncCompletion;
using Microsoft.VisualStudio.Language.Intellisense.AsyncCompletion.Data;
using Microsoft.VisualStudio.Text;
using Microsoft.VisualStudio.Text.Editor;
using Microsoft.VisualStudio.Utilities;
using Microsoft.VisualStudio.Shell;
using System.Threading.Tasks;
using System.Linq;

namespace AICodeAssistant.Features.Completion
{
    /// <summary>
    /// 内联建议处理器，基于Visual Studio标准API实现
    /// 提供更好的用户体验和性能
    /// </summary>
    // Disabled to avoid interference with Pure Ghost Text
    // [Export(typeof(IAsyncCompletionCommitManagerProvider))]
    [Name("AI Inline Suggestion Handler")]
    [ContentType("code")]
    [ContentType("text")]
    public class InlineSuggestionHandlerProvider : IAsyncCompletionCommitManagerProvider
    {
        public IAsyncCompletionCommitManager GetOrCreate(ITextView textView)
        {
            return textView.Properties.GetOrCreateSingletonProperty(
                typeof(InlineSuggestionHandler),
                () => new InlineSuggestionHandler(textView));
        }
    }

    /// <summary>
    /// 内联建议处理器实现
    /// </summary>
    public class InlineSuggestionHandler : IAsyncCompletionCommitManager
    {
        private readonly ITextView _textView;
        private InlineSuggestionDisplay _currentDisplay;

        public InlineSuggestionHandler(ITextView textView)
        {
            _textView = textView ?? throw new ArgumentNullException(nameof(textView));
            
            // 监听键盘事件
            _textView.KeyDown += OnKeyDown;
            _textView.Closed += OnTextViewClosed;

            ActivityLog.LogInformation("InlineSuggestionHandler", "内联建议处理器初始化完成");
        }

        public bool ShouldCommitCompletion(
            IAsyncCompletionSession session,
            SnapshotPoint location,
            char typedChar,
            CancellationToken token)
        {
            try
            {
                // 检查是否是内联补全项
                var selectedItem = session.GetComputedItems(token).SelectedItem;
                if (selectedItem?.CompletionItem?.Properties?.ContainsProperty("IsInlineCompletion") == true)
                {
                    // 对于内联补全，我们有特殊的处理逻辑
                    return false; // 不使用标准的提交逻辑
                }

                return false; // 让其他处理器处理
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineSuggestionHandler", $"判断是否提交失败: {ex.Message}");
                return false;
            }
        }

        public CommitResult TryCommit(
            IAsyncCompletionSession session,
            ITextBuffer buffer,
            CompletionItem item,
            char typedChar,
            CancellationToken token)
        {
            try
            {
                // 检查是否是内联补全项
                if (item.Properties.ContainsProperty("IsInlineCompletion"))
                {
                    // 显示内联建议而不是直接提交
                    ShowInlineSuggestion(item, session);
                    return CommitResult.Handled; // 表示我们已经处理了
                }

                return CommitResult.Unhandled;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineSuggestionHandler", $"提交补全失败: {ex.Message}");
                return CommitResult.Unhandled;
            }
        }

        /// <summary>
        /// 显示内联建议
        /// </summary>
        private void ShowInlineSuggestion(CompletionItem item, IAsyncCompletionSession session)
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                // 获取建议文本
                if (!item.Properties.TryGetProperty("InlineSuggestion", out string suggestion))
                {
                    return;
                }

                // 清除之前的显示
                ClearInlineSuggestion();

                // 获取当前光标位置
                var caretPosition = _textView.Caret.Position.BufferPosition;

                // 创建新的内联显示
                _currentDisplay = new InlineSuggestionDisplay(_textView, suggestion, caretPosition);
                _currentDisplay.Show();

                // 关闭补全会话
                session.Dismiss();

                ActivityLog.LogInformation("InlineSuggestionHandler", $"显示内联建议: '{suggestion}'");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineSuggestionHandler", $"显示内联建议失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 键盘事件处理
        /// </summary>
        private void OnKeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                if (_currentDisplay == null)
                    return;

                switch (e.Key)
                {
                    case Key.Tab:
                        // Tab键接受建议
                        AcceptInlineSuggestion();
                        e.Handled = true;
                        break;

                    case Key.Escape:
                        // Esc键拒绝建议
                        ClearInlineSuggestion();
                        e.Handled = true;
                        break;

                    case Key.Left:
                    case Key.Right:
                    case Key.Up:
                    case Key.Down:
                    case Key.Home:
                    case Key.End:
                        // 方向键清除建议
                        ClearInlineSuggestion();
                        break;

                    default:
                        // 其他键继续输入时清除建议
                        if (char.IsLetterOrDigit((char)KeyInterop.VirtualKeyFromKey(e.Key)) ||
                            e.Key == Key.Space || e.Key == Key.Enter)
                        {
                            ClearInlineSuggestion();
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineSuggestionHandler", $"处理键盘事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 接受内联建议
        /// </summary>
        private void AcceptInlineSuggestion()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                if (_currentDisplay == null)
                    return;

                var suggestion = _currentDisplay.Suggestion;
                var position = _currentDisplay.Position;

                // 插入建议文本
                var edit = _textView.TextBuffer.CreateEdit();
                edit.Insert(position.Position, suggestion);
                edit.Apply();

                ActivityLog.LogInformation("InlineSuggestionHandler", $"接受内联建议: '{suggestion}'");

                // 清除显示
                ClearInlineSuggestion();
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineSuggestionHandler", $"接受内联建议失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除内联建议
        /// </summary>
        private void ClearInlineSuggestion()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                if (_currentDisplay != null)
                {
                    _currentDisplay.Hide();
                    _currentDisplay.Dispose();
                    _currentDisplay = null;
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineSuggestionHandler", $"清除内联建议失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 文本视图关闭事件
        /// </summary>
        private void OnTextViewClosed(object sender, EventArgs e)
        {
            try
            {
                ClearInlineSuggestion();

                if (_textView != null)
                {
                    _textView.KeyDown -= OnKeyDown;
                    _textView.Closed -= OnTextViewClosed;
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineSuggestionHandler", $"清理资源失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 内联建议显示类，负责渲染灰色文本
    /// </summary>
    public class InlineSuggestionDisplay : IDisposable
    {
        private readonly ITextView _textView;
        public string Suggestion { get; }
        public SnapshotPoint Position { get; }

        private IAdornmentLayer _adornmentLayer;
        private bool _isShowing;

        public InlineSuggestionDisplay(ITextView textView, string suggestion, SnapshotPoint position)
        {
            _textView = textView ?? throw new ArgumentNullException(nameof(textView));
            Suggestion = suggestion ?? throw new ArgumentNullException(nameof(suggestion));
            Position = position;

            _adornmentLayer = _textView.GetAdornmentLayer("InlineSuggestion");
        }

        /// <summary>
        /// 显示内联建议
        /// </summary>
        public void Show()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                if (_isShowing)
                    return;

                // 创建灰色文本元素
                var textBlock = new System.Windows.Controls.TextBlock
                {
                    Text = Suggestion,
                    Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Gray),
                    Opacity = 0.6,
                    FontFamily = _textView.FormattedLineSource.DefaultTextProperties.Typeface.FontFamily,
                    FontSize = _textView.FormattedLineSource.DefaultTextProperties.FontRenderingEmSize,
                    FontStyle = System.Windows.FontStyles.Italic // 斜体显示更明显
                };

                // 创建跨度
                var span = new SnapshotSpan(Position, 0);

                // 添加到装饰层
                _adornmentLayer.AddAdornment(
                    Microsoft.VisualStudio.Text.Editor.AdornmentPositioningBehavior.TextRelative,
                    span,
                    "InlineSuggestion",
                    textBlock,
                    null);

                _isShowing = true;

                ActivityLog.LogInformation("InlineSuggestionDisplay", $"显示内联建议: '{Suggestion}'");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineSuggestionDisplay", $"显示失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 隐藏内联建议
        /// </summary>
        public void Hide()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                if (!_isShowing)
                    return;

                _adornmentLayer?.RemoveAllAdornments();
                _isShowing = false;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineSuggestionDisplay", $"隐藏失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                Hide();
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("InlineSuggestionDisplay", $"释放资源失败: {ex.Message}");
            }
        }
    }
}
