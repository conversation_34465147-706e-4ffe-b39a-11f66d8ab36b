using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using Microsoft.VisualStudio.Language.Intellisense.AsyncCompletion;
using Microsoft.VisualStudio.Language.Intellisense.AsyncCompletion.Data;
using Microsoft.VisualStudio.Shell;
using Microsoft.VisualStudio.Text;
using Microsoft.VisualStudio.Text.Editor;

using AICodeAssistant.Services;
using AICodeAssistant.Resources;
using AICodeAssistant.UI;

namespace AICodeAssistant.Features.Completion
{
    /// <summary>
    /// AI 代码补全源实现
    /// </summary>
    public class AICompletionSource : IAsyncCompletionSource
    {
        private readonly ILlmProvider _llmProvider;
        private readonly ITextView _textView;
        private readonly CompletionSettings _settings;

        public AICompletionSource(ITextView textView, ILlmProvider llmProvider, CompletionSettings settings)
        {
            _textView = textView ?? throw new ArgumentNullException(nameof(textView));
            _llmProvider = llmProvider ?? throw new ArgumentNullException(nameof(llmProvider));
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
        }

        public CompletionStartData InitializeCompletion(CompletionTrigger trigger, SnapshotPoint triggerLocation, CancellationToken token)
        {
            try
            {
                ActivityLog.LogInformation("AICompletionSource",
                    $"InitializeCompletion 被调用 - 触发原因: {trigger.Reason}, 字符: '{trigger.Character}', 位置: {triggerLocation.Position}");

                // 检查是否应该参与补全
                var shouldTrigger = ShouldTriggerCompletion(trigger, triggerLocation);
                ActivityLog.LogInformation("AICompletionSource", $"是否应该触发补全: {shouldTrigger}");

                if (!shouldTrigger)
                {
                    ActivityLog.LogInformation("AICompletionSource", "不参与补全");
                    return CompletionStartData.DoesNotParticipateInCompletion;
                }

                // 确定适用范围
                var applicableToSpan = GetApplicableToSpan(triggerLocation);

                ActivityLog.LogInformation("AICompletionSource",
                    $"参与补全 - 类型: {trigger.Reason}, 字符: '{trigger.Character}', 位置: {triggerLocation.Position}, 适用范围: {applicableToSpan}");

                return new CompletionStartData(
                    participation: CompletionParticipation.ProvidesItems,
                    applicableToSpan: applicableToSpan);
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AICompletionSource", $"初始化补全失败: {ex.Message}");
                return CompletionStartData.DoesNotParticipateInCompletion;
            }
        }

        public async Task<CompletionContext> GetCompletionContextAsync(
            IAsyncCompletionSession session,
            CompletionTrigger trigger,
            SnapshotPoint triggerLocation,
            SnapshotSpan applicableToSpan,
            CancellationToken token)
        {
            try
            {
                // 检查取消令牌
                token.ThrowIfCancellationRequested();

                // 提取代码上下文
                var context = await ContextExtractor.ExtractContextAsync(_textView, _settings.ContextLines).ConfigureAwait(false);
                if (context == null)
                {
                    return new CompletionContext(ImmutableArray<CompletionItem>.Empty);
                }

                // 构建补全提示词
                var prompt = BuildCompletionPrompt(context);

                ActivityLog.LogInformation("AICompletionSource", $"开始 AI 补全请求，上下文长度: {prompt.Length}");

                // 调用 AI 生成补全建议
                var response = await _llmProvider.SendAsync(prompt, false, null, token).ConfigureAwait(false);

                if (string.IsNullOrWhiteSpace(response))
                {
                    return new CompletionContext(ImmutableArray<CompletionItem>.Empty);
                }

                // 解析 AI 响应并创建补全项
                var completionItems = ParseCompletionResponse(response, applicableToSpan);

                ActivityLog.LogInformation("AICompletionSource", $"生成了 {completionItems.Length} 个补全建议");

                return new CompletionContext(completionItems);
            }
            catch (OperationCanceledException)
            {
                ActivityLog.LogInformation("AICompletionSource", "补全请求被取消");
                return new CompletionContext(ImmutableArray<CompletionItem>.Empty);
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AICompletionSource", $"获取补全上下文失败: {ex.Message}");
                return new CompletionContext(ImmutableArray<CompletionItem>.Empty);
            }
        }

        public Task<object> GetDescriptionAsync(IAsyncCompletionSession session, CompletionItem item, CancellationToken token)
        {
            try
            {
                // 从补全项的属性中获取详细描述
                if (item.Properties.TryGetProperty(Constants.Completion.DescriptionProperty, out string description))
                {
                    return Task.FromResult<object>(description);
                }

                // 默认描述
                return Task.FromResult<object>($"AI 建议: {item.DisplayText}");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AICompletionSource", $"获取补全描述失败: {ex.Message}");
                return Task.FromResult<object>($"AI 建议: {item.DisplayText}");
            }
        }

        /// <summary>
        /// 判断是否应该触发补全
        /// </summary>
        private bool ShouldTriggerCompletion(CompletionTrigger trigger, SnapshotPoint triggerLocation)
        {
            try
            {
                // 检查是否启用了补全功能
                if (!_settings.Enabled)
                {
                    return false;
                }

                // 检查文件类型是否支持
                var filePath = GetFilePath();
                if (!IsSupportedFileType(filePath))
                {
                    return false;
                }

                var snapshot = triggerLocation.Snapshot;
                var line = snapshot.GetLineFromPosition(triggerLocation);
                var lineText = line.GetText();
                var position = triggerLocation.Position - line.Start.Position;

                switch (trigger.Reason)
                {
                    case CompletionTriggerReason.Insertion:
                        // 字符插入触发
                        if (Constants.Completion.TriggerCharacters.Contains(trigger.Character))
                        {
                            return true;
                        }

                        // 检查是否输入了足够的字符
                        if (position >= Constants.Completion.MinTriggerLength)
                        {
                            var wordStart = GetWordStart(lineText, position);
                            var currentWord = lineText.Substring(wordStart, position - wordStart);
                            return currentWord.Length >= Constants.Completion.MinTriggerLength &&
                                   char.IsLetter(currentWord[0]);
                        }
                        break;

                    case CompletionTriggerReason.Invoke:
                        // 手动调用（Ctrl+Space）
                        return true;

                    case CompletionTriggerReason.Backspace:
                        // 退格键触发
                        if (position > 0)
                        {
                            var wordStart = GetWordStart(lineText, position);
                            var currentWord = lineText.Substring(wordStart, position - wordStart);
                            return currentWord.Length >= Constants.Completion.MinTriggerLength;
                        }
                        break;
                }

                return false;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AICompletionSource", $"判断触发条件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取补全适用的文本范围
        /// </summary>
        private SnapshotSpan GetApplicableToSpan(SnapshotPoint triggerLocation)
        {
            try
            {
                var snapshot = triggerLocation.Snapshot;
                var line = snapshot.GetLineFromPosition(triggerLocation);
                var lineText = line.GetText();
                var position = triggerLocation.Position - line.Start.Position;

                // 找到当前单词的开始位置
                var wordStart = GetWordStart(lineText, position);

                // 找到当前单词的结束位置
                var wordEnd = GetWordEnd(lineText, position);

                var startPosition = line.Start.Position + wordStart;
                var endPosition = line.Start.Position + wordEnd;

                return new SnapshotSpan(snapshot, startPosition, endPosition - startPosition);
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AICompletionSource", $"获取适用范围失败: {ex.Message}");
                // 返回空范围
                return new SnapshotSpan(triggerLocation, 0);
            }
        }

        /// <summary>
        /// 构建补全提示词
        /// </summary>
        private string BuildCompletionPrompt(CodeContext context)
        {
            try
            {
                var language = GetLanguageFromFilePath(context.FilePath);

                // 使用提示词模板
                var systemPrompt = PromptTemplates.ReplaceTokens(
                    PromptTemplates.CodeCompletionSystem,
                    language,
                    context.FilePath);

                var userPrompt = PromptTemplates.ReplaceTokens(
                    PromptTemplates.CodeCompletionUser,
                    language,
                    context.FilePath,
                    ("{BEFORE_CURSOR}", context.BeforeCaret ?? string.Empty),
                    ("{AFTER_CURSOR}", context.AfterCaret ?? string.Empty));

                // 如果有语法上下文，添加额外信息
                if (context.SyntaxContext != null)
                {
                    userPrompt += $"\n\n当前语法节点: {context.SyntaxContext.CurrentNode}";
                    if (!string.IsNullOrEmpty(context.SyntaxContext.ContainingMember))
                    {
                        userPrompt += $"\n包含方法: {context.SyntaxContext.ContainingMember.Split('\n')[0]}";
                    }
                }

                return $"{systemPrompt}\n\n{userPrompt}";
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AICompletionSource", $"构建提示词失败: {ex.Message}");
                return "请补全以下代码：\n" + (context.BeforeCaret ?? string.Empty);
            }
        }

        /// <summary>
        /// 解析 AI 响应并创建补全项
        /// </summary>
        private ImmutableArray<CompletionItem> ParseCompletionResponse(string response, SnapshotSpan applicableToSpan)
        {
            try
            {
                var completionItems = new List<CompletionItem>();

                // 清理响应文本
                var cleanedResponse = CleanAiResponse(response);
                if (string.IsNullOrWhiteSpace(cleanedResponse))
                {
                    return ImmutableArray<CompletionItem>.Empty;
                }

                // 按行分割建议
                var suggestions = cleanedResponse.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries)
                    .Select(s => s.Trim())
                    .Where(s => !string.IsNullOrEmpty(s) && !s.StartsWith("//") && !s.StartsWith("/*"))
                    .Take(Constants.Completion.MaxCompletionItems)
                    .ToList();

                for (int i = 0; i < suggestions.Count; i++)
                {
                    var suggestion = suggestions[i];
                    var completionItem = CreateCompletionItem(suggestion, applicableToSpan, i);
                    if (completionItem != null)
                    {
                        completionItems.Add(completionItem);
                    }
                }

                return completionItems.ToImmutableArray();
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AICompletionSource", $"解析补全响应失败: {ex.Message}");
                return ImmutableArray<CompletionItem>.Empty;
            }
        }

        /// <summary>
        /// 创建补全项
        /// </summary>
        private CompletionItem CreateCompletionItem(string suggestion, SnapshotSpan applicableToSpan, int index)
        {
            try
            {
                // 提取显示文本和插入文本
                var displayText = ExtractDisplayText(suggestion);
                var insertText = ExtractInsertText(suggestion);

                if (string.IsNullOrWhiteSpace(displayText))
                {
                    return null;
                }

                // 确定补全类型
                var completionType = DetermineCompletionType(suggestion);

                // 创建补全项 - 使用简化的构造函数
                var completionItem = new CompletionItem(displayText, this, applicableToSpan);

                // 设置插入文本（如果与显示文本不同）
                if (insertText != displayText)
                {
                    completionItem.Properties.AddProperty("InsertText", insertText);
                }

                // 添加属性
                completionItem.Properties.AddProperty(Constants.Completion.DescriptionProperty,
                    $"AI 建议的 {completionType}: {displayText}");
                completionItem.Properties.AddProperty(Constants.Completion.SourceProperty, "AI");
                completionItem.Properties.AddProperty(Constants.Completion.CompletionTypeProperty, completionType);
                completionItem.Properties.AddProperty(Constants.Completion.PriorityProperty,
                    Constants.Completion.HighPriority - index * 10);

                return completionItem;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AICompletionSource", $"创建补全项失败: {ex.Message}");
                return null;
            }
        }

        #region 辅助方法

        /// <summary>
        /// 获取文件路径
        /// </summary>
        private string GetFilePath()
        {
            try
            {
                if (_textView.TextBuffer.Properties.TryGetProperty(typeof(ITextDocument), out ITextDocument document))
                {
                    return document.FilePath ?? "Unknown";
                }
                return "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// 检查文件类型是否支持
        /// </summary>
        private bool IsSupportedFileType(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || filePath == "Unknown")
                return true; // 默认支持

            var extension = System.IO.Path.GetExtension(filePath)?.ToLowerInvariant();
            return Constants.FileExtensions.SupportedCodeFiles.Contains(extension);
        }

        /// <summary>
        /// 获取单词开始位置
        /// </summary>
        private int GetWordStart(string lineText, int position)
        {
            var start = position;
            while (start > 0 && (char.IsLetterOrDigit(lineText[start - 1]) || lineText[start - 1] == '_'))
            {
                start--;
            }
            return start;
        }

        /// <summary>
        /// 获取单词结束位置
        /// </summary>
        private int GetWordEnd(string lineText, int position)
        {
            var end = position;
            while (end < lineText.Length && (char.IsLetterOrDigit(lineText[end]) || lineText[end] == '_'))
            {
                end++;
            }
            return end;
        }

        /// <summary>
        /// 从文件路径获取编程语言
        /// </summary>
        private string GetLanguageFromFilePath(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return "text";

            var extension = System.IO.Path.GetExtension(filePath)?.ToLowerInvariant();
            return extension switch
            {
                ".cs" => "csharp",
                ".vb" => "vb",
                ".cpp" or ".c" or ".h" or ".hpp" => "cpp",
                ".js" => "javascript",
                ".ts" => "typescript",
                ".py" => "python",
                ".java" => "java",
                ".xml" or ".xaml" => "xml",
                ".json" => "json",
                ".html" => "html",
                ".css" => "css",
                ".sql" => "sql",
                _ => "text"
            };
        }

        /// <summary>
        /// 清理 AI 响应文本
        /// </summary>
        private string CleanAiResponse(string response)
        {
            if (string.IsNullOrWhiteSpace(response))
                return string.Empty;

            // 移除代码块标记
            response = Regex.Replace(response, @"```[\w]*\n?", "", RegexOptions.IgnoreCase);
            response = Regex.Replace(response, @"```", "", RegexOptions.IgnoreCase);

            // 移除常见的 AI 回复前缀
            var prefixesToRemove = new[]
            {
                "这里是补全建议：",
                "建议的代码补全：",
                "代码补全：",
                "补全代码：",
                "Here's the completion:",
                "Suggested completion:",
                "Code completion:"
            };

            foreach (var prefix in prefixesToRemove)
            {
                if (response.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
                {
                    response = response.Substring(prefix.Length).TrimStart();
                    break;
                }
            }

            return response.Trim();
        }

        /// <summary>
        /// 提取显示文本
        /// </summary>
        private string ExtractDisplayText(string suggestion)
        {
            if (string.IsNullOrWhiteSpace(suggestion))
                return string.Empty;

            // 移除行尾的分号和空白字符
            var cleaned = suggestion.Trim().TrimEnd(';');

            // 如果是方法调用，只显示方法名部分
            if (cleaned.Contains('('))
            {
                var parenIndex = cleaned.IndexOf('(');
                var beforeParen = cleaned.Substring(0, parenIndex).Trim();

                // 如果包含点号，只取最后一部分
                if (beforeParen.Contains('.'))
                {
                    var lastDotIndex = beforeParen.LastIndexOf('.');
                    return beforeParen.Substring(lastDotIndex + 1) + cleaned.Substring(parenIndex);
                }

                return cleaned;
            }

            // 如果包含点号，只取最后一部分
            if (cleaned.Contains('.'))
            {
                var lastDotIndex = cleaned.LastIndexOf('.');
                return cleaned.Substring(lastDotIndex + 1);
            }

            return cleaned;
        }

        /// <summary>
        /// 提取插入文本
        /// </summary>
        private string ExtractInsertText(string suggestion)
        {
            if (string.IsNullOrWhiteSpace(suggestion))
                return string.Empty;

            // 移除行尾的分号和空白字符
            return suggestion.Trim().TrimEnd(';');
        }

        /// <summary>
        /// 确定补全类型
        /// </summary>
        private string DetermineCompletionType(string suggestion)
        {
            if (string.IsNullOrWhiteSpace(suggestion))
                return Constants.Completion.TypeSnippet;

            var cleaned = suggestion.Trim();

            if (cleaned.Contains('(') && cleaned.Contains(')'))
                return Constants.Completion.TypeMethod;

            if (cleaned.Contains("class ") || cleaned.Contains("interface ") || cleaned.Contains("struct "))
                return Constants.Completion.TypeClass;

            if (cleaned.Contains(" = ") || cleaned.Contains("var ") || cleaned.Contains("let "))
                return Constants.Completion.TypeVariable;

            if (cleaned.Contains("get;") || cleaned.Contains("set;") || cleaned.Contains("{ get") || cleaned.Contains("{ set"))
                return Constants.Completion.TypeProperty;

            // 检查是否是关键字
            var keywords = new[] { "if", "else", "for", "while", "switch", "case", "try", "catch", "finally", "using", "namespace" };
            if (keywords.Any(k => cleaned.StartsWith(k + " ") || cleaned.Equals(k)))
                return Constants.Completion.TypeKeyword;

            return Constants.Completion.TypeSnippet;
        }



        #endregion
    }
}
