namespace AICodeAssistant
{
    /// <summary>
    /// 应用程序常量定义
    /// </summary>
    public static class Constants
    {
        /// <summary>
        /// 应用程序名称
        /// </summary>
        public const string AppName = "OllamaAICodeAssistant";

        /// <summary>
        /// 配置文件相关
        /// </summary>
        public static class Config
        {
            public const string AppDataFolder = "OllamaAICodeAssistant";
            public const string ChatLogFileName = "chatlog.json";
            public const string SettingsFileName = "settings.json";
            public const string ModelCacheFileName = "OllamaModelCache.json";
        }

        /// <summary>
        /// 默认设置值
        /// </summary>
        public static class Defaults
        {
            public const int MaxContextLines = 20;
            public const int MaxTokens = 2048;
            public const double Temperature = 0.7;
            public const int TimeoutSeconds = 30;
            public const int MaxContextLength = 8192; // 默认上下文 Token 数 (8K)，最大支持 256K
            public const string DefaultOllamaModel = "codellama";
        }

        /// <summary>
        /// Ollama API 相关常量
        /// </summary>
        public static class Ollama
        {
            public const string DefaultApiBase = "http://localhost:11434";
            public const string TagsEndpoint = "/api/tags";
            public const string GenerateEndpoint = "/api/generate";
            public const string ChatEndpoint = "/api/chat";
            public const string ModelNotAvailableText = "无法获取模型列表";
        }

        /// <summary>
        /// UI 相关常量
        /// </summary>
        public static class UI
        {
            public const string ChatWindowTitle = "AI Code Assistant";
            public const string CompletionDisplayText = "AI Suggestion";
            public const string LoadingText = "正在生成...";
            public const string ErrorText = "生成失败，请重试";
        }

        /// <summary>
        /// 命令相关常量
        /// </summary>
        public static class Commands
        {
            public const string SearchCommand = "/search";
            public const string DocCommand = "/doc";
            public const string ExplainCommand = "/explain";
            public const string RefactorCommand = "/refactor";
            public const string TestCommand = "/test";
            public const string ClearCommand = "/clear";
            public const string ExportCommand = "/export";
        }

        /// <summary>
        /// 文件扩展名
        /// </summary>
        public static class FileExtensions
        {
            public static readonly string[] SupportedCodeFiles =
            {
                ".cs", ".vb", ".cpp", ".c", ".h", ".hpp", ".js", ".ts",
                ".py", ".java", ".xml", ".xaml", ".json", ".html", ".css", ".sql"
            };
        }

        /// <summary>
        /// 代码补全相关常量
        /// </summary>
        public static class Completion
        {
            public const string DescriptionProperty = "AICompletion.Description";
            public const string SourceProperty = "AICompletion.Source";
            public const string CompletionTypeProperty = "AICompletion.Type";
            public const string PriorityProperty = "AICompletion.Priority";

            // 触发字符
            public static readonly char[] TriggerCharacters = { '.', '(', '[', '<', ' ', '\t' };

            // 补全类型
            public const string TypeMethod = "Method";
            public const string TypeProperty = "Property";
            public const string TypeVariable = "Variable";
            public const string TypeClass = "Class";
            public const string TypeKeyword = "Keyword";
            public const string TypeSnippet = "Snippet";

            // 优先级
            public const int HighPriority = 1000;
            public const int MediumPriority = 500;
            public const int LowPriority = 100;

            // 最小触发长度
            public const int MinTriggerLength = 2;

            // 最大补全项数量
            public const int MaxCompletionItems = 10;
        }
    }
}
