﻿<?xml version="1.0" encoding="utf-8"?>
<CommandTable xmlns="http://schemas.microsoft.com/VisualStudio/2005-10-18/CommandTable" xmlns:xs="http://www.w3.org/2001/XMLSchema">

  <Extern href="stdidcmd.h"/>
  <Extern href="vsshlids.h"/>
  <Include href="KnownImageIds.vsct"/>
  <Include href="VSGlobals.vsct"/>

  <Commands package="AICodeAssistant">
    <!--This section defines the elements the user can interact with, like a menu command or a button or combo box in a toolbar. -->
    <Buttons>
      <Button guid="AICodeAssistant" id="MyCommand" priority="0x0100" type="Button">
		<Parent guid="VSMainMenu" id="Extensions.DefaultGroup"/>
        <Icon guid="ImageCatalogGuid" id="Comment" />
        <CommandFlag>IconIsMoniker</CommandFlag>
        <Strings>
          <ButtonText>AI Code Assistant</ButtonText>
          <LocCanonicalName>.View.AICodeAssistant</LocCanonicalName>
        </Strings>
      </Button>
      <Button guid="AICodeAssistant" id="SettingsCommand" priority="0x0101" type="Button">
        <Parent guid="VSMainMenu" id="Extensions.DefaultGroup"/>
        <Icon guid="ImageCatalogGuid" id="Settings" />
        <CommandFlag>IconIsMoniker</CommandFlag>
        <Strings>
          <ButtonText>AI Code Assistant 设置</ButtonText>
          <LocCanonicalName>.Tools.AICodeAssistantSettings</LocCanonicalName>
        </Strings>
      </Button>
    </Buttons>
  </Commands>

  <Symbols>
    <GuidSymbol name="AICodeAssistant" value="{f33ece25-ef14-485c-9f53-0b7a0388b8ad}">
      <IDSymbol name="MyCommand" value="0x0100" />
      <IDSymbol name="SettingsCommand" value="0x0101" />
    </GuidSymbol>
  </Symbols>
</CommandTable>
