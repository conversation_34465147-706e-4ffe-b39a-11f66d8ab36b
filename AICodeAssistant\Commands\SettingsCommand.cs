using System.Threading.Tasks;
using System.Windows;
using Microsoft.VisualStudio.Shell;
using AICodeAssistant.UI;

namespace AICodeAssistant.Commands
{
    [Command(PackageIds.SettingsCommand)]
    internal sealed class SettingsCommand : BaseCommand<SettingsCommand>
    {
        protected override async Task ExecuteAsync(OleMenuCmdEventArgs e)
        {
            await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();

            var settingsWindow = new Window
            {
                Title = "AI Code Assistant 设置",
                Content = new SettingsPage(),
                Width = 600,
                Height = 700,
                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                ResizeMode = ResizeMode.CanResize
            };

            settingsWindow.ShowDialog();
        }
    }
}
