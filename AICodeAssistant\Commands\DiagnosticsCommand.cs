using System;
using System.ComponentModel.Design;
using Microsoft.VisualStudio.Shell;
using AICodeAssistant.Tests;

namespace AICodeAssistant.Commands
{
    /// <summary>
    /// 诊断命令，用于检查代码补全功能状态
    /// </summary>
    internal sealed class DiagnosticsCommand
    {
        /// <summary>
        /// 命令 ID
        /// </summary>
        public const int CommandId = 0x0102;

        /// <summary>
        /// 命令菜单组 GUID
        /// </summary>
        public static readonly Guid CommandSet = new Guid("f33ece25-ef14-485c-9f53-0b7a0388b8ad");

        /// <summary>
        /// VS 包
        /// </summary>
        private readonly AsyncPackage package;

        /// <summary>
        /// 初始化命令的新实例
        /// </summary>
        /// <param name="package">所有者包，不能为 null</param>
        /// <param name="commandService">命令服务，用于添加命令到 VS</param>
        private DiagnosticsCommand(AsyncPackage package, OleMenuCommandService commandService)
        {
            this.package = package ?? throw new ArgumentNullException(nameof(package));
            commandService = commandService ?? throw new ArgumentNullException(nameof(commandService));

            var menuCommandID = new CommandID(CommandSet, CommandId);
            var menuItem = new MenuCommand(this.Execute, menuCommandID);
            commandService.AddCommand(menuItem);
        }

        /// <summary>
        /// 获取此命令的实例
        /// </summary>
        public static DiagnosticsCommand Instance
        {
            get;
            private set;
        }

        /// <summary>
        /// 获取提供此命令的服务提供者
        /// </summary>
        private Microsoft.VisualStudio.Shell.IAsyncServiceProvider ServiceProvider
        {
            get
            {
                return this.package;
            }
        }

        /// <summary>
        /// 初始化此命令的单例实例
        /// </summary>
        /// <param name="package">所有者包，不能为 null</param>
        public static async System.Threading.Tasks.Task InitializeAsync(AsyncPackage package)
        {
            // 切换到主线程 - 包初始化需要在 UI 线程上进行
            await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync(package.DisposalToken);

            OleMenuCommandService commandService = await package.GetServiceAsync(typeof(IMenuCommandService)) as OleMenuCommandService;
            Instance = new DiagnosticsCommand(package, commandService);
        }

        /// <summary>
        /// 此函数是在用户单击菜单项时调用的回调
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void Execute(object sender, EventArgs e)
        {
            ThreadHelper.ThrowIfNotOnUIThread();

            try
            {
                // 记录诊断信息到活动日志
                CompletionDiagnostics.LogDiagnosticsToActivityLog();

                // 运行 MEF 组件测试
                MefComponentTest.RunAllTests();

                // 运行补全响应测试
                CompletionResponseTest.RunAllTests();

                // 显示消息框
                string message = "代码补全功能诊断已完成！\n\n" +
                               "诊断信息已记录到 Visual Studio 活动日志中。\n\n" +
                               "查看方法：\n" +
                               "1. 帮助 → 发送反馈 → 报告问题\n" +
                               "2. 点击 '查看详细信息'\n" +
                               "3. 查找 'CompletionDiagnostics' 和 'AICompletionSource' 相关日志\n\n" +
                               "如果补全功能仍未工作，请检查：\n" +
                               "• Ollama 服务是否正在运行\n" +
                               "• OpenAI API 密钥是否正确\n" +
                               "• 补全功能是否在设置中启用\n" +
                               "• 尝试重启 Visual Studio";

                string title = "AI 代码补全诊断";

                // 显示消息框
                VsShellUtilities.ShowMessageBox(
                    this.package,
                    message,
                    title,
                    OLEMSGICON.OLEMSGICON_INFO,
                    OLEMSGBUTTON.OLEMSGBUTTON_OK,
                    OLEMSGDEFBUTTON.OLEMSGDEFBUTTON_FIRST);

                // 同时记录到活动日志
                ActivityLog.LogInformation("DiagnosticsCommand", "用户执行了补全功能诊断");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("DiagnosticsCommand", $"执行诊断命令失败: {ex.Message}");
                
                string errorMessage = $"执行诊断时出现错误：\n{ex.Message}\n\n请查看活动日志获取详细信息。";
                VsShellUtilities.ShowMessageBox(
                    this.package,
                    errorMessage,
                    "诊断错误",
                    OLEMSGICON.OLEMSGICON_ERROR,
                    OLEMSGBUTTON.OLEMSGBUTTON_OK,
                    OLEMSGDEFBUTTON.OLEMSGDEFBUTTON_FIRST);
            }
        }
    }
}
