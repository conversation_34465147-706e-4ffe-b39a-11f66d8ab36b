using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.VisualStudio.Language.Intellisense.AsyncCompletion;
using Microsoft.VisualStudio.Text;
using Microsoft.VisualStudio.Text.Editor;
using Microsoft.VisualStudio.Utilities;
using Microsoft.VisualStudio.Shell;
using AICodeAssistant.Services;
using AICodeAssistant.UI;
using AICodeAssistant.Resources;

namespace AICodeAssistant.Features.Completion
{
    /// <summary>
    /// AI 内联代码补全提供者，提供灰色文本预览功能
    /// </summary>
    [Export(typeof(IInlineCompletionProvider))]
    [Name("AI Inline Completion")]
    [ContentType("code")]
    [ContentType("text")]
    public class AIInlineCompletionProvider : IInlineCompletionProvider
    {
        private readonly Dictionary<ITextView, AIInlineCompletionSession> _sessions = new Dictionary<ITextView, AIInlineCompletionSession>();

        public async Task<IInlineCompletionResult> GetCompletionAsync(
            ITextView textView,
            SnapshotPoint triggerPoint,
            CancellationToken cancellationToken)
        {
            try
            {
                ActivityLog.LogInformation("AIInlineCompletionProvider", $"内联补全请求 - 位置: {triggerPoint.Position}");

                // 检查是否应该触发补全
                if (!ShouldTriggerInlineCompletion(textView, triggerPoint))
                {
                    return null;
                }

                // 获取或创建会话
                var session = GetOrCreateSession(textView);
                if (session == null)
                {
                    return null;
                }

                // 生成补全建议
                var completion = await session.GetCompletionAsync(triggerPoint, cancellationToken);
                if (completion != null)
                {
                    ActivityLog.LogInformation("AIInlineCompletionProvider", $"生成内联补全: '{completion.Text}'");
                }

                return completion;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AIInlineCompletionProvider", $"内联补全失败: {ex.Message}");
                return null;
            }
        }

        public void Dispose()
        {
            foreach (var session in _sessions.Values)
            {
                session?.Dispose();
            }
            _sessions.Clear();
        }

        /// <summary>
        /// 判断是否应该触发内联补全
        /// </summary>
        private bool ShouldTriggerInlineCompletion(ITextView textView, SnapshotPoint triggerPoint)
        {
            try
            {
                // 获取当前行
                var line = triggerPoint.GetContainingLine();
                var lineText = line.GetText();
                var position = triggerPoint.Position - line.Start.Position;

                // 检查是否在行尾
                if (position < lineText.Length)
                {
                    return false;
                }

                // 检查行是否有足够的内容
                var trimmedLine = lineText.TrimEnd();
                if (trimmedLine.Length < 3)
                {
                    return false;
                }

                // 检查是否是支持的文件类型
                var filePath = GetFilePath(textView);
                if (!IsSupportedFileType(filePath))
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AIInlineCompletionProvider", $"判断触发条件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取或创建补全会话
        /// </summary>
        private AIInlineCompletionSession GetOrCreateSession(ITextView textView)
        {
            try
            {
                if (_sessions.TryGetValue(textView, out var existingSession))
                {
                    return existingSession;
                }

                // 加载设置
                var settings = LoadSettings();
                if (settings?.Completion?.Enabled != true)
                {
                    return null;
                }

                // 获取 LLM 提供者
                var llmProvider = GetLlmProvider(settings);
                if (llmProvider == null || !llmProvider.IsAvailable)
                {
                    return null;
                }

                // 创建新会话
                var session = new AIInlineCompletionSession(textView, llmProvider, settings.Completion);
                _sessions[textView] = session;

                // 监听文本视图关闭事件
                textView.Closed += (sender, e) =>
                {
                    if (_sessions.TryGetValue(textView, out var sessionToRemove))
                    {
                        sessionToRemove.Dispose();
                        _sessions.Remove(textView);
                    }
                };

                ActivityLog.LogInformation("AIInlineCompletionProvider", "创建新的内联补全会话");
                return session;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AIInlineCompletionProvider", $"创建补全会话失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 加载设置
        /// </summary>
        private AppSettings LoadSettings()
        {
            try
            {
                return SettingsManager.LoadSettings() ?? AppSettings.CreateDefault();
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AIInlineCompletionProvider", $"加载设置失败: {ex.Message}");
                return AppSettings.CreateDefault();
            }
        }

        /// <summary>
        /// 获取 LLM 提供者
        /// </summary>
        private ILlmProvider GetLlmProvider(AppSettings settings)
        {
            try
            {
                var options = CreateLlmOptions(settings);
                return LlmProviderFactory.CreateProvider(settings.ProviderType, options);
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("AIInlineCompletionProvider", $"创建 LLM 提供者失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建 LLM 选项
        /// </summary>
        private LlmOptions CreateLlmOptions(AppSettings settings)
        {
            return settings.ProviderType switch
            {
                LlmProviderType.OpenAI => new LlmOptions
                {
                    ApiKey = settings.OpenAI.ApiKey,
                    ApiBase = settings.OpenAI.ApiBase,
                    Model = settings.OpenAI.Model,
                    Temperature = settings.Completion.Temperature,
                    MaxTokens = Math.Min(settings.Completion.MaxTokens, 512), // 限制内联补全的长度
                    TimeoutSeconds = 15 // 更短的超时时间
                },
                LlmProviderType.Ollama => new LlmOptions
                {
                    ApiBase = settings.Ollama.ApiBase,
                    Model = settings.Ollama.Model,
                    Temperature = settings.Completion.Temperature,
                    MaxTokens = Math.Min(settings.Completion.MaxTokens, 512),
                    TimeoutSeconds = 20
                },
                _ => LlmProviderFactory.GetDefaultOptions(LlmProviderType.Ollama)
            };
        }

        /// <summary>
        /// 获取文件路径
        /// </summary>
        private string GetFilePath(ITextView textView)
        {
            try
            {
                if (textView.TextBuffer.Properties.TryGetProperty(typeof(ITextDocument), out ITextDocument document))
                {
                    return document.FilePath ?? "Unknown";
                }
            }
            catch
            {
                // 忽略异常
            }
            
            return "Unknown";
        }

        /// <summary>
        /// 检查文件类型是否支持
        /// </summary>
        private bool IsSupportedFileType(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || filePath == "Unknown")
                return true;

            var extension = System.IO.Path.GetExtension(filePath)?.ToLowerInvariant();
            return Constants.FileExtensions.SupportedCodeFiles.Contains(extension);
        }
    }

    /// <summary>
    /// 内联补全结果接口
    /// </summary>
    public interface IInlineCompletionResult
    {
        string Text { get; }
        SnapshotSpan ApplicableToSpan { get; }
    }

    /// <summary>
    /// 内联补全提供者接口
    /// </summary>
    public interface IInlineCompletionProvider : IDisposable
    {
        Task<IInlineCompletionResult> GetCompletionAsync(
            ITextView textView,
            SnapshotPoint triggerPoint,
            CancellationToken cancellationToken);
    }

    /// <summary>
    /// 内联补全结果实现
    /// </summary>
    public class InlineCompletionResult : IInlineCompletionResult
    {
        public string Text { get; }
        public SnapshotSpan ApplicableToSpan { get; }

        public InlineCompletionResult(string text, SnapshotSpan applicableToSpan)
        {
            Text = text ?? string.Empty;
            ApplicableToSpan = applicableToSpan;
        }
    }
}
