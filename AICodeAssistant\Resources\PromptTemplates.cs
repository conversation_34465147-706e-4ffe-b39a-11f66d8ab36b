namespace AICodeAssistant.Resources
{
    /// <summary>
    /// LLM 提示词模板
    /// </summary>
    public static class PromptTemplates
    {
        /// <summary>
        /// 代码补全系统提示词
        /// </summary>
        public const string CodeCompletionSystem = @"你是一个专业的 {LANGUAGE} 代码补全助手。请根据给定的代码上下文，提供简洁准确的代码补全建议。

重要规则：
1. 只返回代码补全内容，不要任何解释文字
2. 每行一个建议，最多提供5个建议
3. 确保语法正确且符合 {LANGUAGE} 规范
4. 优先使用上下文中已有的变量和方法
5. 不要重复光标前已有的代码

文件类型：{LANGUAGE}
文件路径：{FILE_PATH}";

        /// <summary>
        /// 代码补全用户提示词
        /// </summary>
        public const string CodeCompletionUser = @"代码上下文：
{BEFORE_CURSOR}▮{AFTER_CURSOR}

请补全光标位置（▮）的代码，每行一个建议：";

        /// <summary>
        /// 聊天助手系统提示词
        /// </summary>
        public const string ChatAssistantSystem = @"你是 Visual Studio 内的 AI 编程助手，专门帮助开发者解决编程问题。你具备以下能力：

1. **代码分析**：理解和分析各种编程语言的代码
2. **问题解答**：回答编程相关的技术问题
3. **代码优化**：提供代码改进建议
4. **错误诊断**：帮助定位和修复代码错误
5. **最佳实践**：分享编程最佳实践和设计模式

当前工作环境：
- IDE: Visual Studio
- 当前文件：{FILE_PATH}
- 编程语言：{LANGUAGE}

请用中文回答，保持专业、准确和有帮助。如果涉及代码，请使用适当的代码块格式。";

        /// <summary>
        /// 聊天用户提示词（带选中代码）
        /// </summary>
        public const string ChatUserWithSelection = @"当前文件：{FILE_PATH}

选中的代码：
```{LANGUAGE}
{SELECTED_CODE}
```

用户问题：{USER_MESSAGE}";

        /// <summary>
        /// 聊天用户提示词（无选中代码）
        /// </summary>
        public const string ChatUserWithoutSelection = @"当前文件：{FILE_PATH}

用户问题：{USER_MESSAGE}";

        /// <summary>
        /// 代码解释提示词
        /// </summary>
        public const string ExplainCodePrompt = @"请详细解释以下 {LANGUAGE} 代码的功能、逻辑和关键概念：

```{LANGUAGE}
{CODE}
```

请包括：
1. 代码的主要功能
2. 关键逻辑和算法
3. 使用的设计模式或技术
4. 可能的改进建议";

        /// <summary>
        /// 代码重构提示词
        /// </summary>
        public const string RefactorCodePrompt = @"请对以下 {LANGUAGE} 代码进行重构，提高其可读性、性能和可维护性：

```{LANGUAGE}
{CODE}
```

请提供：
1. 重构后的代码
2. 重构的理由和改进点
3. 性能或可维护性的提升说明";

        /// <summary>
        /// 生成测试代码提示词
        /// </summary>
        public const string GenerateTestPrompt = @"请为以下 {LANGUAGE} 代码生成单元测试：

```{LANGUAGE}
{CODE}
```

请生成：
1. 完整的测试类
2. 覆盖主要功能的测试方法
3. 边界条件和异常情况的测试
4. 使用适当的测试框架（如 xUnit、NUnit 等）";

        /// <summary>
        /// 文档生成提示词
        /// </summary>
        public const string GenerateDocPrompt = @"请为以下 {LANGUAGE} 代码生成详细的文档注释：

```{LANGUAGE}
{CODE}
```

请生成：
1. 类和方法的 XML 文档注释
2. 参数和返回值的说明
3. 使用示例（如果适用）
4. 异常情况的说明";

        /// <summary>
        /// 搜索相关代码提示词
        /// </summary>
        public const string SearchCodePrompt = @"在当前项目中搜索与以下关键词相关的代码和概念：

搜索关键词：{SEARCH_TERMS}
当前文件：{FILE_PATH}

请提供：
1. 相关的代码模式或实现
2. 可能的使用场景
3. 最佳实践建议
4. 相关的类库或框架推荐";

        /// <summary>
        /// 替换模板中的占位符
        /// </summary>
        /// <param name="template">模板字符串</param>
        /// <param name="language">编程语言</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="additionalReplacements">额外的替换参数</param>
        /// <returns>替换后的字符串</returns>
        public static string ReplaceTokens(string template, string language = "", string filePath = "", params (string token, string value)[] additionalReplacements)
        {
            var result = template
                .Replace("{LANGUAGE}", language ?? "text")
                .Replace("{FILE_PATH}", filePath ?? "Unknown");

            if (additionalReplacements != null)
            {
                foreach (var (token, value) in additionalReplacements)
                {
                    result = result.Replace(token, value ?? string.Empty);
                }
            }

            return result;
        }
    }
}
