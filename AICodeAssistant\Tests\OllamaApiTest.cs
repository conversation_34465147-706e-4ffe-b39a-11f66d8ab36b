using System;
using System.Text.Json;

namespace AICodeAssistant.Tests
{
    /// <summary>
    /// Ollama API 格式测试
    /// </summary>
    public static class OllamaApiTest
    {
        /// <summary>
        /// 测试 messages 格式的构建和解析
        /// </summary>
        public static void TestMessagesFormat()
        {
            Console.WriteLine("=== Ollama API Messages 格式测试 ===");

            // 测试1：基本的 messages 格式
            var basicMessages = new
            {
                messages = new[]
                {
                    new { role = "system", content = "你是一个专业的代码助手。" },
                    new { role = "user", content = "为什么天空是蓝色的？" },
                    new { role = "assistant", content = "由于瑞利散射的原因。" },
                    new { role = "user", content = "这与米氏散射有什么不同？" }
                }
            };

            var json = JsonSerializer.Serialize(basicMessages, new JsonSerializerOptions { WriteIndented = true });
            Console.WriteLine("基本 Messages 格式:");
            Console.WriteLine(json);
            Console.WriteLine();

            // 测试2：完整的 Ollama Chat API 请求格式
            var chatRequest = new
            {
                model = "codellama",
                messages = new[]
                {
                    new { role = "system", content = "你是一个专业的代码助手，专门帮助开发者解决编程问题。" },
                    new { role = "user", content = "请解释一下 C# 中的异步编程概念。" }
                },
                stream = true,
                options = new
                {
                    temperature = 0.7,
                    num_predict = 2048
                }
            };

            var chatJson = JsonSerializer.Serialize(chatRequest, new JsonSerializerOptions { WriteIndented = true });
            Console.WriteLine("完整 Ollama Chat API 请求格式:");
            Console.WriteLine(chatJson);
            Console.WriteLine();

            // 测试3：验证 JSON 解析
            try
            {
                using var document = JsonDocument.Parse(chatJson);
                var hasMessages = document.RootElement.TryGetProperty("messages", out var messagesElement);
                Console.WriteLine($"包含 messages 属性: {hasMessages}");
                
                if (hasMessages)
                {
                    Console.WriteLine($"Messages 数组长度: {messagesElement.GetArrayLength()}");
                    
                    foreach (var messageElement in messagesElement.EnumerateArray())
                    {
                        var role = messageElement.GetProperty("role").GetString();
                        var content = messageElement.GetProperty("content").GetString();
                        Console.WriteLine($"  - {role}: {content.Substring(0, Math.Min(50, content.Length))}...");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"JSON 解析失败: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("=== 测试完成 ===");
        }

        /// <summary>
        /// 测试响应格式解析
        /// </summary>
        public static void TestResponseFormats()
        {
            Console.WriteLine("=== Ollama API 响应格式测试 ===");

            // 测试1：Generate API 响应格式
            var generateResponse = @"{
  ""model"": ""codellama"",
  ""created_at"": ""2024-01-01T00:00:00Z"",
  ""response"": ""这是生成的回复内容。"",
  ""done"": true
}";

            Console.WriteLine("Generate API 响应格式:");
            Console.WriteLine(generateResponse);
            
            try
            {
                using var doc1 = JsonDocument.Parse(generateResponse);
                if (doc1.RootElement.TryGetProperty("response", out var responseElement))
                {
                    Console.WriteLine($"提取的内容: {responseElement.GetString()}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析失败: {ex.Message}");
            }

            Console.WriteLine();

            // 测试2：Chat API 响应格式
            var chatResponse = @"{
  ""model"": ""codellama"",
  ""created_at"": ""2024-01-01T00:00:00Z"",
  ""message"": {
    ""role"": ""assistant"",
    ""content"": ""这是聊天回复内容。""
  },
  ""done"": true
}";

            Console.WriteLine("Chat API 响应格式:");
            Console.WriteLine(chatResponse);
            
            try
            {
                using var doc2 = JsonDocument.Parse(chatResponse);
                if (doc2.RootElement.TryGetProperty("message", out var messageElement) &&
                    messageElement.TryGetProperty("content", out var contentElement))
                {
                    Console.WriteLine($"提取的内容: {contentElement.GetString()}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析失败: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("=== 响应格式测试完成 ===");
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            TestMessagesFormat();
            Console.WriteLine();
            TestResponseFormats();
        }
    }
}
