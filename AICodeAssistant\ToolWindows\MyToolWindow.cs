using System;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.VisualStudio.Imaging;

namespace AICodeAssistant
{
    public class ChatToolWindow : BaseToolWindow<ChatToolWindow>
    {
        public override string GetTitle(int toolWindowId) => Constants.UI.ChatWindowTitle;

        public override Type PaneType => typeof(Pane);

        public override Task<FrameworkElement> CreateAsync(int toolWindowId, CancellationToken cancellationToken)
        {
            return Task.FromResult<FrameworkElement>(new ChatToolWindowControl());
        }

        [Guid("4daf335e-d286-4288-b7b2-f75a0556e989")]
        internal class Pane : ToolkitToolWindowPane
        {
            public Pane()
            {
                BitmapImageMoniker = KnownMonikers.Comment;
            }
        }
    }
}