using System.Text.Json.Serialization;
using AICodeAssistant.Services;

namespace AICodeAssistant.UI
{
    /// <summary>
    /// 应用程序设置模型
    /// </summary>
    public class AppSettings
    {
        [JsonPropertyName("providerType")]
        public LlmProviderType ProviderType { get; set; } = LlmProviderType.Ollama;

        [JsonPropertyName("openai")]
        public OpenAISettings OpenAI { get; set; } = new OpenAISettings();

        [JsonPropertyName("ollama")]
        public OllamaSettings Ollama { get; set; } = new OllamaSettings();

        [JsonPropertyName("completion")]
        public CompletionSettings Completion { get; set; } = new CompletionSettings();

        [JsonPropertyName("chat")]
        public ChatSettings Chat { get; set; } = new ChatSettings();

        [JsonPropertyName("security")]
        public SecuritySettings Security { get; set; } = new SecuritySettings();

        public static AppSettings CreateDefault()
        {
            return new AppSettings();
        }
    }

    public class OpenAISettings
    {
        [JsonPropertyName("apiKey")]
        public string ApiKey { get; set; } = string.Empty;

        [JsonPropertyName("apiBase")]
        public string ApiBase { get; set; } = "https://api.openai.com";

        [JsonPropertyName("model")]
        public string Model { get; set; } = "gpt-3.5-turbo";
    }

    public class OllamaSettings
    {
        [JsonPropertyName("apiBase")]
        public string ApiBase { get; set; } = "http://localhost:11434";

        [JsonPropertyName("model")]
        public string Model { get; set; } = "codellama";
    }

    public class CompletionSettings
    {
        [JsonPropertyName("enabled")]
        public bool Enabled { get; set; } = true;

        [JsonPropertyName("contextLines")]
        public int ContextLines { get; set; } = Constants.Defaults.MaxContextLines;

        [JsonPropertyName("maxTokens")]
        public int MaxTokens { get; set; } = Constants.Defaults.MaxTokens;

        [JsonPropertyName("temperature")]
        public double Temperature { get; set; } = Constants.Defaults.Temperature;
    }

    public class ChatSettings
    {
        [JsonPropertyName("maxContextLength")]
        public int MaxContextLength { get; set; } = Constants.Defaults.MaxContextLength;

        [JsonPropertyName("maxOutputTokens")]
        public int MaxOutputTokens { get; set; } = Constants.Defaults.MaxTokens;

        [JsonPropertyName("autoSave")]
        public bool AutoSave { get; set; } = true;
    }

    public class SecuritySettings
    {
        [JsonPropertyName("maskSensitiveData")]
        public bool MaskSensitiveData { get; set; } = true;
    }
}
